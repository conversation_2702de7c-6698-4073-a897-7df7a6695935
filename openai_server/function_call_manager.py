#!/usr/bin/env python3
"""
函数调用管理器 - 实现OpenAI Function Calling标准
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass


@dataclass
class FunctionCallDecision:
    """函数调用决策结果"""
    should_call: bool
    function_name: Optional[str] = None
    arguments: Optional[Dict] = None
    reason: Optional[str] = None


class FunctionCallManager:
    """函数调用管理器 - 实现OpenAI Function Calling标准"""
    
    def __init__(self, logger):
        self.logger = logger
        self.function_definitions = self._get_standard_function_definitions()
    
    def should_call_function(self, messages: List[Dict], functions: Optional[List[Dict]] = None, 
                           function_call: Optional[Union[str, Dict]] = None) -> FunctionCallDecision:
        """判断是否需要调用函数
        
        Args:
            messages: 对话消息列表
            functions: 可用函数定义列表
            function_call: 函数调用控制参数
            
        Returns:
            FunctionCallDecision: 函数调用决策结果
        """
        # 如果没有提供函数定义，使用默认定义
        if not functions:
            functions = self.function_definitions
        
        # 提取用户的最新消息
        user_message = self._extract_latest_user_message(messages)
        if not user_message:
            return FunctionCallDecision(False, reason="No user message found")
        
        # 检查是否包含图片
        has_image = self._has_image_content(user_message)
        if not has_image:
            return FunctionCallDecision(False, reason="No image content found")
        
        # 根据function_call参数决定调用策略
        if function_call == "none":
            return FunctionCallDecision(False, reason="Function calling disabled")
        
        elif isinstance(function_call, dict) and "name" in function_call:
            # 强制调用指定函数
            function_name = function_call["name"]
            if self._is_function_available(function_name, functions):
                arguments = self._extract_function_arguments(user_message, function_name, functions)
                return FunctionCallDecision(
                    True, function_name, arguments, 
                    f"Forced call to {function_name}"
                )
            else:
                return FunctionCallDecision(
                    False, reason=f"Function {function_name} not available"
                )
        
        elif function_call == "auto" or function_call is None:
            # 智能判断是否需要调用函数
            return self._auto_decide_function_call(user_message, functions)
        
        else:
            return FunctionCallDecision(False, reason=f"Invalid function_call parameter: {function_call}")
    
    def _extract_latest_user_message(self, messages: List[Dict]) -> Optional[Dict]:
        """提取最新的用户消息"""
        for message in reversed(messages):
            if message.get("role") == "user":
                return message
        return None
    
    def _has_image_content(self, message: Dict) -> bool:
        """检查消息是否包含图片内容"""
        content = message.get("content", "")
        
        if isinstance(content, list):
            return any(item.get("type") == "image_url" for item in content)
        elif isinstance(content, str):
            # 检查是否包含图片URL或base64图片
            return bool(re.search(r'https?://.*\.(jpg|jpeg|png|gif|webp)', content, re.IGNORECASE) or
                       'data:image/' in content)
        
        return False
    
    def _is_function_available(self, function_name: str, functions: List[Dict]) -> bool:
        """检查函数是否可用"""
        return any(func.get("name") == function_name for func in functions)
    
    def _auto_decide_function_call(self, user_message: Dict, functions: List[Dict]) -> FunctionCallDecision:
        """智能决定是否调用函数"""
        content = self._extract_text_content(user_message)
        
        # 分析用户意图
        intent_scores = self._analyze_user_intent(content)
        
        # 如果有明确的感知需求，选择最合适的函数
        if intent_scores:
            best_function = max(intent_scores.items(), key=lambda x: x[1])
            function_name, score = best_function
            
            if score > 0:  # 有匹配的意图
                arguments = self._extract_function_arguments(user_message, function_name, functions)
                return FunctionCallDecision(
                    True, function_name, arguments,
                    f"Auto-selected {function_name} (score: {score})"
                )
        
        # 默认使用check_image进行基础对象检测
        if self._is_function_available("check_image", functions):
            arguments = self._extract_function_arguments(user_message, "check_image", functions)
            return FunctionCallDecision(
                True, "check_image", arguments,
                "Default to check_image for image analysis"
            )
        
        return FunctionCallDecision(False, reason="No suitable function found")
    
    def _extract_text_content(self, message: Dict) -> str:
        """提取消息中的文本内容"""
        content = message.get("content", "")
        text_parts = []
        
        if isinstance(content, list):
            for item in content:
                if item.get("type") == "text":
                    text_parts.append(item.get("text", ""))
        elif isinstance(content, str):
            text_parts.append(content)
        
        return " ".join(text_parts).strip()
    
    def _analyze_user_intent(self, text: str) -> Dict[str, int]:
        """分析用户意图，返回函数名和匹配分数"""
        text_lower = text.lower()
        
        # 关键词映射
        intent_keywords = {
            "split_image": ["split", "segment", "separate", "divide", "cut", "分割", "分离"],
            "props_describe": ["describe", "property", "detail", "what is", "tell me about", 
                             "描述", "属性", "详细", "特征"],
            "angle_prediction": ["angle", "rotation", "orientation", "direction", "rotate",
                               "角度", "旋转", "方向"],
            "key_point_prediction": ["keypoint", "key point", "landmark", "joints", "skeleton",
                                   "关键点", "骨架", "节点"],
            "grab_point_prediction": ["grab", "grasp", "pick", "hold", "manipulation", "robot",
                                    "抓取", "抓住", "机器人"],
            "full_perception": ["full", "complete", "comprehensive", "everything", "all", "analyze",
                              "全面", "完整", "综合", "分析"],
            "check_image": ["detect", "find", "locate", "identify", "check", "see", "look",
                          "检测", "发现", "识别", "查看"]
        }
        
        # 计算匹配分数
        scores = {}
        for function_name, keywords in intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                scores[function_name] = score
        
        return scores
    
    def _extract_function_arguments(self, user_message: Dict, function_name: str, 
                                  functions: List[Dict]) -> Dict:
        """提取函数调用参数"""
        # 获取函数定义
        function_def = next((f for f in functions if f.get("name") == function_name), None)
        if not function_def:
            return {}
        
        arguments = {}
        
        # 提取图片URL
        image_url = self._extract_image_url(user_message)
        if image_url:
            arguments["image_url"] = image_url
        
        # 提取对象名称
        text_content = self._extract_text_content(user_message)
        object_names = self._extract_object_names(text_content)
        if object_names:
            arguments["object_names"] = object_names
        else:
            # 使用默认对象名称
            arguments["object_names"] = ["object", "car", "person"]
        
        # 对于需要questions参数的函数，提取或生成问题
        if function_name in ["props_describe", "full_perception"]:
            questions = self._extract_questions(text_content, function_name)
            arguments["questions"] = questions
        
        return arguments
    
    def _extract_image_url(self, message: Dict) -> Optional[str]:
        """提取图片URL"""
        content = message.get("content", "")
        
        if isinstance(content, list):
            for item in content:
                if item.get("type") == "image_url":
                    return item.get("image_url", {}).get("url")
        elif isinstance(content, str):
            # 尝试从文本中提取URL
            url_match = re.search(r'https?://[^\s]+\.(jpg|jpeg|png|gif|webp)', content, re.IGNORECASE)
            if url_match:
                return url_match.group(0)
        
        return None
    
    def _extract_object_names(self, text: str) -> List[str]:
        """从文本中提取对象名称"""
        # 常见对象关键词
        common_objects = [
            "car", "vehicle", "automobile", "truck", "bus", "汽车", "车辆",
            "person", "people", "human", "man", "woman", "child", "人", "人员",
            "building", "house", "structure", "建筑", "房屋",
            "tree", "plant", "flower", "树", "植物", "花",
            "animal", "dog", "cat", "bird", "动物", "狗", "猫", "鸟",
            "object", "thing", "item", "物体", "东西"
        ]
        
        text_lower = text.lower()
        found_objects = []
        
        for obj in common_objects:
            if obj in text_lower:
                # 避免重复添加相似的对象
                if obj not in found_objects:
                    found_objects.append(obj)
        
        # 去重并保持顺序
        return list(dict.fromkeys(found_objects))
    
    def _extract_questions(self, text: str, function_name: str) -> List[str]:
        """提取或生成问题"""
        # 尝试从文本中提取问题
        questions = []
        
        # 查找问号结尾的句子
        question_pattern = r'[^.!?]*\?'
        found_questions = re.findall(question_pattern, text)
        questions.extend([q.strip() + '?' for q in found_questions if q.strip()])
        
        # 如果没有找到问题，根据函数类型生成默认问题
        if not questions:
            if function_name == "props_describe":
                questions = ["请详细描述这个对象的特征和属性"]
            elif function_name == "full_perception":
                questions = ["请对这张图片进行全面的感知分析"]
            else:
                questions = ["请分析这张图片"]
        
        return questions
    
    def _get_standard_function_definitions(self) -> List[Dict]:
        """获取标准化的函数定义"""
        return [
            {
                "name": "check_image",
                "description": "检测和定位图像中的对象，返回对象位置、类别和置信度",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分析的图像URL"},
                        "object_names": {
                            "type": "array", 
                            "items": {"type": "string"},
                            "description": "要检测的对象名称列表"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            },
            {
                "name": "split_image",
                "description": "对图像进行语义分割，将不同对象分离出来",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分割的图像URL"},
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"}, 
                            "description": "要分割的对象类别"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            },
            {
                "name": "props_describe", 
                "description": "详细描述图像中对象的属性特征",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分析的图像URL"},
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要描述的对象名称"
                        },
                        "questions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "具体要回答的问题"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            },
            {
                "name": "angle_prediction",
                "description": "预测图像中对象的角度和方向信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分析的图像URL"},
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要分析角度的对象名称"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            },
            {
                "name": "key_point_prediction",
                "description": "预测图像中对象的关键点位置",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分析的图像URL"},
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要分析关键点的对象名称"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            },
            {
                "name": "grab_point_prediction",
                "description": "为机器人预测最佳的对象抓取点",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分析的图像URL"},
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要分析抓取点的对象名称"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            },
            {
                "name": "full_perception",
                "description": "对图像进行全面的感知分析，包括检测、分割、描述等",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_url": {"type": "string", "description": "要分析的图像URL"},
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要分析的对象名称"
                        },
                        "questions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要回答的具体问题"
                        }
                    },
                    "required": ["image_url", "object_names"]
                }
            }
        ]
    
    def validate_function_call(self, function_name: str, arguments: Dict, 
                             functions: Optional[List[Dict]] = None) -> Tuple[bool, str]:
        """验证函数调用的有效性
        
        Args:
            function_name: 函数名
            arguments: 函数参数
            functions: 函数定义列表
            
        Returns:
            Tuple[是否有效, 错误信息]
        """
        if not functions:
            functions = self.function_definitions
        
        # 查找函数定义
        function_def = next((f for f in functions if f.get("name") == function_name), None)
        if not function_def:
            return False, f"Function '{function_name}' not found"
        
        # 验证必需参数
        required_params = function_def.get("parameters", {}).get("required", [])
        for param in required_params:
            if param not in arguments:
                return False, f"Missing required parameter: {param}"
        
        # 验证参数类型
        properties = function_def.get("parameters", {}).get("properties", {})
        for param_name, param_value in arguments.items():
            if param_name in properties:
                expected_type = properties[param_name].get("type")
                if not self._validate_parameter_type(param_value, expected_type):
                    return False, f"Invalid type for parameter '{param_name}': expected {expected_type}"
        
        return True, "Valid function call"
    
    def _validate_parameter_type(self, value: Any, expected_type: str) -> bool:
        """验证参数类型"""
        if expected_type == "string":
            return isinstance(value, str)
        elif expected_type == "array":
            return isinstance(value, list)
        elif expected_type == "object":
            return isinstance(value, dict)
        elif expected_type == "number":
            return isinstance(value, (int, float))
        elif expected_type == "boolean":
            return isinstance(value, bool)
        else:
            return True  # 未知类型，允许通过
