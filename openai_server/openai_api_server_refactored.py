#!/usr/bin/env python3
"""
FastAPI server providing OpenAI-compatible REST API for QJ Robots Perception.

This server exposes the perception capabilities through standard OpenAI API endpoints,
making it compatible with existing OpenAI client libraries and applications.

Architecture:
    - Backend API: This server provides pure API services (port 8000)
    - Frontend: Static files served by nginx (front-end/back-end separation)
    - Demo page: https://jwd.vooice.tech/perception/demo.html

Usage:
    python openai_api_server_refactored.py

API Endpoints:
    POST /v1/chat/completions    - Chat completions (OpenAI compatible)
    GET  /v1/models             - List available models
    GET  /v1/functions          - List available functions
    GET  /health                - Health check
    GET  /docs                  - API documentation
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Optional, Union

from fastapi import FastAPI, HTTPException, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

import sys
import os
# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from py_qj_robots.perception import Perception
from utils.token_config import TokenManager
from perception_handler import PerceptionHandler
from response_generator import ResponseGenerator, FunctionCallResponseGenerator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建处理器实例
perception_handler = PerceptionHandler(logger)
response_generator = ResponseGenerator(perception_handler, logger)
function_call_generator = FunctionCallResponseGenerator(logger)

# FastAPI app
app = FastAPI(
    title="QJ Robots Perception API",
    description="OpenAI-compatible API for QJ Robots Perception capabilities",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Token manager
token_manager = TokenManager()

# 静态文件现在由nginx提供服务（前后端分离架构）

# Pydantic models
class ContentItem(BaseModel):
    type: str
    text: Optional[str] = None
    image_url: Optional[Dict[str, str]] = None

class Message(BaseModel):
    role: str
    content: Union[str, List[ContentItem]]

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Message]
    functions: Optional[List[Dict]] = None  # OpenAI Function Calling support
    function_call: Optional[Union[str, Dict]] = None  # OpenAI Function Calling support
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False


# Helper functions
def extract_image_and_text(messages):
    """Extract image and text from messages"""
    image_data = None
    text_content = ""
    
    for message in messages:
        if message.get('role') == 'user':
            content = message.get('content', '')
            if isinstance(content, list):
                for item in content:
                    if item.get('type') == 'text':
                        text_content += item.get('text', '') + ' '
                    elif item.get('type') == 'image_url':
                        image_data = item.get('image_url', {}).get('url')
            elif isinstance(content, str):
                text_content += content + ' '
    
    return image_data, text_content.strip()


def process_image_data(image_data):
    """Process image data and return URL"""
    if not image_data:
        return None
    
    if image_data.startswith('data:image'):
        # Base64 image data - not yet supported
        logger.warning("Base64 image data not yet supported")
        return None
    else:
        # Direct image URL
        return image_data


# API endpoints
@app.post("/v1/chat/completions")
async def create_chat_completion(
    request: ChatCompletionRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Create a chat completion with OpenAI Function Calling support"""
    try:
        # 验证token并获取凭据
        token = credentials.credentials
        creds = token_manager.get_credentials(token)
        if not creds:
            raise HTTPException(status_code=401, detail="Invalid API key")

        app_id, app_secret = creds

        # 设置环境变量
        os.environ['QJ_APP_ID'] = app_id
        os.environ['QJ_APP_SECRET'] = app_secret

        # Convert Pydantic models to dicts
        messages = [msg.model_dump() for msg in request.messages]

        # 创建感知API实例
        perception = Perception()

        # 检查是否是函数结果处理请求
        if _is_function_result_request(messages):
            logger.info("Processing function result request")
            return await response_generator.create_function_result_response(
                request, perception, messages
            )

        # 检查是否支持Function Calling
        if hasattr(request, 'functions') and request.functions:
            logger.info("Processing Function Calling request")
            return await response_generator.create_function_calling_response(
                request, perception, messages
            )

        # 传统模式：直接执行感知API
        logger.info("Processing traditional perception request")

        # 提取图片和文字
        try:
            image_data, text_content = extract_image_and_text(messages)
            image_url = process_image_data(image_data) or ""
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"图片处理错误: {str(e)}")

        # 智能确定要调用的函数
        function_name, function_params = perception_handler.determine_function(
            text_content,
            getattr(request, 'functions', None),
            getattr(request, 'function_call', None)
        )

        logger.info(f"Selected function: {function_name} for request: {text_content[:100]}...")

        # 根据stream参数选择处理方式
        if request.stream:
            return await response_generator.create_streaming_response(
                request, perception, image_url, text_content,
                function_name, function_params
            )
        else:
            return await response_generator.create_non_streaming_response(
                request, perception, image_url, text_content,
                function_name, function_params
            )

    except Exception as e:
        logger.error(f"Chat completion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def _is_function_result_request(messages: List[Dict]) -> bool:
    """检查是否是函数结果处理请求"""
    # 查找是否有function角色的消息
    for msg in messages:
        if msg.get("role") == "function":
            return True

    # 查找是否有assistant的function_call消息
    for msg in messages:
        if (msg.get("role") == "assistant" and
            msg.get("function_call") is not None):
            return True

    return False


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.get("/v1/models")
async def list_models():
    """List available models (OpenAI compatible)"""
    return {
        "object": "list",
        "data": [
            {
                "id": "qj-perception-v1",
                "object": "model",
                "created": int(datetime.now().timestamp()),
                "owned_by": "qj-robots"
            }
        ]
    }


@app.get("/v1/functions")
async def list_functions():
    """List available perception functions"""
    functions = [
        {
            "name": "check_image",
            "description": "检测和定位图像中的对象",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要检测的对象名称"}
                },
                "required": ["image_url", "object_names"]
            }
        },
        {
            "name": "split_image", 
            "description": "分割图像中的对象",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要分割的对象名称"}
                },
                "required": ["image_url", "object_names"]
            }
        },
        {
            "name": "props_describe",
            "description": "描述对象的详细属性",
            "parameters": {
                "type": "object", 
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要描述的对象名称"},
                    "questions": {"type": "array", "items": {"type": "string"}, "description": "要回答的问题"}
                },
                "required": ["image_url", "object_names"]
            }
        },
        {
            "name": "angle_prediction",
            "description": "预测对象的角度和方向",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要分析角度的对象名称"}
                },
                "required": ["image_url", "object_names"]
            }
        },
        {
            "name": "key_point_prediction",
            "description": "预测对象的关键点",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要分析关键点的对象名称"}
                },
                "required": ["image_url", "object_names"]
            }
        },
        {
            "name": "grab_point_prediction",
            "description": "预测机器人抓取点",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要分析抓取点的对象名称"}
                },
                "required": ["image_url", "object_names"]
            }
        },
        {
            "name": "full_perception",
            "description": "综合感知分析",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string", "description": "图像URL"},
                    "object_names": {"type": "array", "items": {"type": "string"}, "description": "要分析的对象名称"},
                    "questions": {"type": "array", "items": {"type": "string"}, "description": "要回答的问题"}
                },
                "required": ["image_url", "object_names"]
            }
        }
    ]
    
    return {
        "object": "list",
        "data": functions
    }


# Demo页面现在由nginx直接提供服务
# 访问地址: https://jwd.vooice.tech/perception/demo.html

# 图片上传现在通过前端直接调用腾讯云COS接口
# 上传参数接口: https://jwd.vooice.tech/open-api/open-apis/base/upload/params


if __name__ == "__main__":
    import uvicorn
    
    print("Starting QJ Robots Perception API Server...")
    print("OpenAI-compatible endpoints:")
    print("  POST /v1/chat/completions")
    print("  GET  /v1/models")
    print("  GET  /v1/functions")
    print("  GET  /docs (API documentation)")
    print()
    print("Server will be available at: http://localhost:8000")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
