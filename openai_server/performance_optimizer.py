#!/usr/bin/env python3
"""
性能优化组件
包括缓存机制、请求限流、性能监控等
"""

import time
import hashlib
import json
import asyncio
from typing import Dict, Any, Optional, List
from collections import defaultdict, deque
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    created_at: float
    access_count: int = 0
    last_accessed: float = 0


@dataclass
class PerformanceMetrics:
    """性能指标"""
    request_count: int = 0
    total_response_time: float = 0.0
    error_count: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    function_calls: Dict[str, int] = None
    
    def __post_init__(self):
        if self.function_calls is None:
            self.function_calls = defaultdict(int)
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / self.request_count if self.request_count > 0 else 0.0
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        return self.error_count / self.request_count if self.request_count > 0 else 0.0
    
    @property
    def cache_hit_rate(self) -> float:
        """缓存命中率"""
        total_cache_requests = self.cache_hits + self.cache_misses
        return self.cache_hits / total_cache_requests if total_cache_requests > 0 else 0.0


class PerceptionCache:
    """感知结果缓存"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            ttl: 生存时间（秒）
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order = deque()  # LRU队列
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存结果"""
        if key not in self.cache:
            return None
        
        entry = self.cache[key]
        current_time = time.time()
        
        # 检查是否过期
        if current_time - entry.created_at > self.ttl:
            self._remove_key(key)
            return None
        
        # 更新访问信息
        entry.access_count += 1
        entry.last_accessed = current_time
        
        # 更新LRU顺序
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)
        
        return entry.value
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存结果"""
        current_time = time.time()
        
        # 如果缓存已满，移除最少使用的条目
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_lru()
        
        # 添加或更新缓存条目
        self.cache[key] = CacheEntry(
            value=value,
            created_at=current_time,
            access_count=1,
            last_accessed=current_time
        )
        
        # 更新LRU顺序
        if key in self.access_order:
            self.access_order.remove(key)
        self.access_order.append(key)
    
    def _remove_key(self, key: str) -> None:
        """移除指定键"""
        if key in self.cache:
            del self.cache[key]
        if key in self.access_order:
            self.access_order.remove(key)
    
    def _evict_lru(self) -> None:
        """移除最少使用的条目"""
        if self.access_order:
            lru_key = self.access_order.popleft()
            if lru_key in self.cache:
                del self.cache[lru_key]
    
    def generate_cache_key(self, function_name: str, arguments: Dict) -> str:
        """生成缓存键"""
        # 创建一个包含函数名和参数的字符串
        key_data = {
            "function": function_name,
            "args": arguments
        }
        key_string = json.dumps(key_data, sort_keys=True, ensure_ascii=False)
        
        # 生成MD5哈希
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def clear_expired(self) -> int:
        """清理过期条目"""
        current_time = time.time()
        expired_keys = []
        
        for key, entry in self.cache.items():
            if current_time - entry.created_at > self.ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_key(key)
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        current_time = time.time()
        total_entries = len(self.cache)
        expired_entries = sum(
            1 for entry in self.cache.values()
            if current_time - entry.created_at > self.ttl
        )
        
        return {
            "total_entries": total_entries,
            "expired_entries": expired_entries,
            "valid_entries": total_entries - expired_entries,
            "max_size": self.max_size,
            "usage_rate": total_entries / self.max_size if self.max_size > 0 else 0.0,
            "ttl": self.ttl
        }


class RateLimiter:
    """请求限流器"""
    
    def __init__(self, max_requests: int = 100, time_window: int = 60):
        """
        初始化限流器
        
        Args:
            max_requests: 时间窗口内的最大请求数
            time_window: 时间窗口（秒）
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests: Dict[str, deque] = defaultdict(deque)
    
    def is_allowed(self, client_id: str) -> bool:
        """检查是否允许请求"""
        current_time = time.time()
        client_requests = self.requests[client_id]
        
        # 清理过期的请求记录
        while client_requests and current_time - client_requests[0] > self.time_window:
            client_requests.popleft()
        
        # 检查是否超过限制
        return len(client_requests) < self.max_requests
    
    def record_request(self, client_id: str) -> None:
        """记录请求"""
        current_time = time.time()
        self.requests[client_id].append(current_time)
    
    def get_remaining_requests(self, client_id: str) -> int:
        """获取剩余请求数"""
        current_time = time.time()
        client_requests = self.requests[client_id]
        
        # 清理过期的请求记录
        while client_requests and current_time - client_requests[0] > self.time_window:
            client_requests.popleft()
        
        return max(0, self.max_requests - len(client_requests))
    
    def get_reset_time(self, client_id: str) -> float:
        """获取限制重置时间"""
        client_requests = self.requests[client_id]
        if not client_requests:
            return 0.0
        
        return client_requests[0] + self.time_window


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 1000):
        """
        初始化性能监控器
        
        Args:
            window_size: 滑动窗口大小
        """
        self.window_size = window_size
        self.metrics = PerformanceMetrics()
        self.response_times = deque(maxlen=window_size)
        self.recent_errors = deque(maxlen=100)  # 保留最近的错误
        self.start_time = time.time()
    
    def record_request(self, response_time: float, success: bool = True, 
                      function_name: Optional[str] = None) -> None:
        """记录请求"""
        self.metrics.request_count += 1
        self.metrics.total_response_time += response_time
        self.response_times.append(response_time)
        
        if not success:
            self.metrics.error_count += 1
            self.recent_errors.append({
                "timestamp": time.time(),
                "response_time": response_time,
                "function_name": function_name
            })
        
        if function_name:
            self.metrics.function_calls[function_name] += 1
    
    def record_cache_hit(self) -> None:
        """记录缓存命中"""
        self.metrics.cache_hits += 1
    
    def record_cache_miss(self) -> None:
        """记录缓存未命中"""
        self.metrics.cache_misses += 1
    
    def get_percentile(self, percentile: float) -> float:
        """获取响应时间百分位数"""
        if not self.response_times:
            return 0.0
        
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * percentile / 100)
        return sorted_times[min(index, len(sorted_times) - 1)]
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        uptime = time.time() - self.start_time
        
        return {
            "uptime_seconds": uptime,
            "total_requests": self.metrics.request_count,
            "total_errors": self.metrics.error_count,
            "error_rate": self.metrics.error_rate,
            "average_response_time": self.metrics.average_response_time,
            "p50_response_time": self.get_percentile(50),
            "p95_response_time": self.get_percentile(95),
            "p99_response_time": self.get_percentile(99),
            "cache_hit_rate": self.metrics.cache_hit_rate,
            "cache_hits": self.metrics.cache_hits,
            "cache_misses": self.metrics.cache_misses,
            "function_calls": dict(self.metrics.function_calls),
            "requests_per_second": self.metrics.request_count / uptime if uptime > 0 else 0.0
        }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict]:
        """获取最近的错误"""
        return list(self.recent_errors)[-limit:]
    
    def reset_metrics(self) -> None:
        """重置指标"""
        self.metrics = PerformanceMetrics()
        self.response_times.clear()
        self.recent_errors.clear()
        self.start_time = time.time()


class OptimizedPerceptionHandler:
    """优化的感知处理器"""
    
    def __init__(self, base_handler, cache_size: int = 1000, cache_ttl: int = 3600):
        """
        初始化优化的感知处理器
        
        Args:
            base_handler: 基础感知处理器
            cache_size: 缓存大小
            cache_ttl: 缓存TTL
        """
        self.base_handler = base_handler
        self.cache = PerceptionCache(cache_size, cache_ttl)
        self.monitor = PerformanceMonitor()
        self.rate_limiter = RateLimiter()
    
    async def process_request_with_cache(self, perception, image_url: str, 
                                       text_content: str, function_name: str, 
                                       function_params: Dict, client_id: str = "default") -> str:
        """带缓存的请求处理"""
        start_time = time.time()
        
        try:
            # 检查限流
            if not self.rate_limiter.is_allowed(client_id):
                raise Exception("Rate limit exceeded")
            
            # 记录请求
            self.rate_limiter.record_request(client_id)
            
            # 生成缓存键
            cache_key = self.cache.generate_cache_key(function_name, {
                "image_url": image_url,
                "text_content": text_content,
                **function_params
            })
            
            # 尝试从缓存获取结果
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                self.monitor.record_cache_hit()
                response_time = time.time() - start_time
                self.monitor.record_request(response_time, True, function_name)
                return cached_result
            
            # 缓存未命中，调用基础处理器
            self.monitor.record_cache_miss()
            result = await self.base_handler.process_request(
                perception, image_url, text_content, function_name, function_params
            )
            
            # 缓存结果
            self.cache.set(cache_key, result)
            
            # 记录性能指标
            response_time = time.time() - start_time
            self.monitor.record_request(response_time, True, function_name)
            
            return result
            
        except Exception as e:
            # 记录错误
            response_time = time.time() - start_time
            self.monitor.record_request(response_time, False, function_name)
            raise
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            "performance": self.monitor.get_current_metrics(),
            "cache": self.cache.get_stats(),
            "recent_errors": self.monitor.get_recent_errors()
        }
    
    def cleanup_cache(self) -> int:
        """清理过期缓存"""
        return self.cache.clear_expired()
