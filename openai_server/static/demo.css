/* 全局样式 */
:root {
    --bs-primary-rgb: 13, 110, 253;
    --chat-bg: #f8f9fa;
    --sidebar-bg: #ffffff;
    --message-user-bg: #0d6efd;
    --message-assistant-bg: #f8f9fa;
    --border-color: #dee2e6;
    --text-muted: #6c757d;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

[data-bs-theme="dark"] {
    --chat-bg: #212529;
    --sidebar-bg: #343a40;
    --message-assistant-bg: #343a40;
    --border-color: #495057;
    --text-muted: #adb5bd;
}

html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--chat-bg);
}

.container-fluid {
    padding: 0;
    height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    background: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    height: 100vh;
    box-shadow: var(--shadow-sm);
}

.sidebar-header {
    background: var(--sidebar-bg);
    border-bottom: 1px solid var(--border-color);
}

.chat-history {
    background: var(--chat-bg);
}

.chat-history::-webkit-scrollbar {
    width: 4px;
}

.chat-history::-webkit-scrollbar-track {
    background: transparent;
}

.chat-history::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

.chat-item {
    border: none !important;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--sidebar-bg);
}

.chat-item:hover {
    background: var(--bs-primary);
    color: white;
    transform: translateX(2px);
}

.chat-item.active {
    background: var(--bs-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.chat-item-title {
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-item-preview {
    font-size: 0.75rem;
    opacity: 0.8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.config-panel {
    background: var(--sidebar-bg);
    border-top: 1px solid var(--border-color);
}

/* 主聊天区域样式 */
.main-chat {
    height: 100vh;
    background: var(--chat-bg);
}

.chat-header {
    background: var(--sidebar-bg) !important;
    border-bottom: 1px solid var(--border-color) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.chat-messages {
    background: var(--chat-bg);
    height: calc(100vh - 140px);
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.welcome-message {
    min-height: 60vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 消息样式 */
.message {
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 75%;
    padding: 1rem 1.25rem;
    border-radius: 1.25rem;
    position: relative;
    box-shadow: var(--shadow-sm);
    word-wrap: break-word;
}

.message.user .message-content {
    background: var(--message-user-bg);
    color: white;
    border-bottom-right-radius: 0.5rem;
    margin-left: 3rem;
}

.message.assistant .message-content {
    background: var(--message-assistant-bg);
    color: var(--bs-body-color);
    border-bottom-left-radius: 0.5rem;
    border: 1px solid var(--border-color);
    margin-right: 3rem;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    margin: 0 0.75rem;
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--message-user-bg);
    color: white;
    order: 1;
}

.message.assistant .message-avatar {
    background: var(--bs-secondary);
    color: white;
}

.message-image {
    max-width: 250px;
    border-radius: 0.75rem;
    margin-bottom: 0.75rem;
    box-shadow: var(--shadow-sm);
}

.message-text {
    line-height: 1.6;
    font-size: 0.95rem;
}

.message-text strong {
    font-weight: 600;
}

.message-text em {
    font-style: italic;
}

.streaming-cursor {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: var(--bs-primary);
    animation: blink 1s infinite;
    margin-left: 2px;
    border-radius: 1px;
}

@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

/* 输入区域样式 */
.input-area {
    background: var(--sidebar-bg) !important;
    border-top: 1px solid var(--border-color) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 已上传图片预览样式 */
.uploaded-image-preview {
    animation: fadeInUp 0.3s ease;
}

.uploaded-image-preview img {
    object-fit: cover;
    box-shadow: var(--shadow-sm);
}

.text-input-area textarea {
    border-radius: 1rem !important;
    background: var(--chat-bg) !important;
    border: 1px solid var(--border-color) !important;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    line-height: 1.5;
}

.text-input-area textarea:focus {
    border-color: var(--bs-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25) !important;
    background: var(--sidebar-bg) !important;
}

#sendBtn {
    transition: all 0.2s ease;
}

#sendBtn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: var(--shadow);
}

#sendBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 工具按钮样式 */
.btn-outline-secondary {
    border-color: var(--border-color) !important;
    color: var(--text-muted) !important;
}

.btn-outline-secondary:hover {
    background: var(--bs-primary) !important;
    border-color: var(--bs-primary) !important;
    color: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s;
    }

    .sidebar.show {
        left: 0;
    }

    .main-chat {
        width: 100%;
    }

    .message-content {
        max-width: 85%;
    }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar,
.chat-history::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.chat-history::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb,
.chat-history::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.chat-history::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
}

@keyframes dots {

    0%,
    20% {
        content: '.';
    }

    40% {
        content: '..';
    }

    60%,
    100% {
        content: '...';
    }
}