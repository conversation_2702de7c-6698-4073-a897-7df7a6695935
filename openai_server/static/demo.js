// 全局变量
let currentChatId = null;
let chats = {};
let isProcessing = false;
let currentImageUrl = null;
let currentImageFile = null;

// 初始化
$(document).ready(function () {
    initializeApp();
    bindEvents();
    createNewChat();
});

// 初始化应用
function initializeApp() {
    // 从localStorage加载聊天历史
    const savedChats = localStorage.getItem('perception_chats');
    if (savedChats) {
        chats = JSON.parse(savedChats);
        renderChatList();
    }

    // 根据当前域名自动设置Base URL
    if (window.location.hostname === 'jwd.vooice.tech') {
        $('#baseUrl').val('https://jwd.vooice.tech/openai');
    } else {
        $('#baseUrl').val('http://localhost:8000');
    }
}



// 绑定事件
function bindEvents() {
    // 新建聊天
    $('#newChatBtn').click(createNewChat);

    // 图片上传
    $('#imageUploadBtn').click(() => $('#imageInput').click());
    $('#imageInput').change(handleImageUpload);
    $('#removeImageBtn').click(removeImage);

    // 发送消息
    $('#sendBtn').click(sendMessage);
    $('#messageInput').keypress(function (e) {
        if (e.which === 13 && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 输入框变化
    $('#messageInput').on('input', function () {
        updateSendButton();
        updateCharCount();
    });

    // 聊天选择
    $(document).on('click', '.chat-item', function () {
        const chatId = $(this).data('chat-id');
        switchToChat(chatId);
    });

    // 主题切换
    $('#themeToggle').click(toggleTheme);

    // API Key 可见性切换
    $('#toggleApiKey').click(toggleApiKeyVisibility);

    // 清除历史
    $('#clearHistory').click(clearChatHistory);

    // 导出和分享
    $('#exportChat').click(exportCurrentChat);
    $('#shareChat').click(shareCurrentChat);

    // 工具按钮
    $('#attachFileBtn').click(handleFileAttach);
    $('#voiceInputBtn').click(handleVoiceInput);
    $('#emojiBtn').click(showEmojiPicker);
}

// 创建新聊天
function createNewChat() {
    const chatId = 'chat_' + Date.now();
    const chat = {
        id: chatId,
        title: '新对话',
        messages: [],
        createdAt: new Date().toISOString()
    };

    chats[chatId] = chat;
    currentChatId = chatId;

    renderChatList();
    renderMessages();
    clearInput();

    // 隐藏欢迎消息
    $('.welcome-message').hide();

    saveChats();
}

// 切换聊天
function switchToChat(chatId) {
    if (chats[chatId]) {
        currentChatId = chatId;
        renderMessages();
        renderChatList();
        $('.welcome-message').hide();
    }
}

// 渲染聊天列表
function renderChatList() {
    const $chatList = $('#chatList');
    $chatList.empty();

    const sortedChats = Object.values(chats).sort((a, b) =>
        new Date(b.createdAt) - new Date(a.createdAt)
    );

    // 更新聊天数量
    $('#chatCount').text(sortedChats.length);

    if (sortedChats.length === 0) {
        $chatList.append(`
            <div class="text-center text-muted py-4">
                <i class="bi bi-chat-square-dots mb-2" style="font-size: 2rem;"></i>
                <p class="mb-0">暂无聊天记录</p>
                <small>点击"新建对话"开始</small>
            </div>
        `);
        return;
    }

    sortedChats.forEach(chat => {
        const isActive = chat.id === currentChatId;
        const preview = chat.messages.length > 0 ?
            chat.messages[chat.messages.length - 1].content.substring(0, 40) + '...' :
            '暂无消息';

        const timeAgo = getTimeAgo(new Date(chat.createdAt));

        const $chatItem = $(`
            <div class="list-group-item chat-item ${isActive ? 'active' : ''}" data-chat-id="${chat.id}">
                <div class="d-flex w-100 justify-content-between align-items-start">
                    <div class="flex-grow-1 min-w-0">
                        <div class="chat-item-title">${chat.title}</div>
                        <div class="chat-item-preview">${preview}</div>
                    </div>
                    <small class="text-muted ms-2">${timeAgo}</small>
                </div>
            </div>
        `);

        $chatList.append($chatItem);
    });
}

// 渲染消息
function renderMessages() {
    const $chatMessages = $('#chatMessages');
    $chatMessages.empty();

    if (!currentChatId || !chats[currentChatId]) {
        $chatMessages.append('<div class="welcome-message"><div class="text-center"><i class="fas fa-robot fa-3x text-primary mb-3"></i><h3>欢迎使用感知API测试工具</h3><p class="text-muted">上传图片并输入问题，开始测试感知API的强大功能！</p></div></div>');
        return;
    }

    const chat = chats[currentChatId];
    chat.messages.forEach(message => {
        appendMessage(message.role, message.content, message.imageUrl, false);
    });

    scrollToBottom();
}

// 添加消息到界面
function appendMessage(role, content, imageUrl = null, animate = true) {
    const $chatMessages = $('#chatMessages');
    const avatar = role === 'user' ? '<i class="bi bi-person-fill"></i>' : '<i class="bi bi-robot"></i>';

    const imageHtml = imageUrl ? `<img src="${imageUrl}" class="message-image" alt="上传的图片">` : '';

    const $message = $(`
        <div class="message ${role}">
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                ${imageHtml}
                <div class="message-text">${formatMessage(content)}</div>
            </div>
        </div>
    `);

    if (animate) {
        $message.hide().appendTo($chatMessages).fadeIn(300);
    } else {
        $message.appendTo($chatMessages);
    }

    // 隐藏欢迎消息
    $('.welcome-message').hide();

    scrollToBottom();
}

// 处理图片上传
function handleImageUpload(e) {
    const file = e.target.files[0];
    if (file) {
        handleImageFile(file);
    }
}

// 处理图片文件
function handleImageFile(file) {
    if (!file.type.startsWith('image/')) {
        showToast('文件类型错误', '请选择图片文件！', 'error');
        return;
    }

    // 保存文件对象，用于后续上传
    currentImageFile = file;

    // 显示预览
    const reader = new FileReader();
    reader.onload = function (e) {
        $('#previewImage').attr('src', e.target.result);
        $('#uploadedImagePreview').show();
        $('#uploadStatus').hide(); // 默认隐藏loading状态
        updateSendButton();
    };
    reader.readAsDataURL(file);

    // 创建本地预览URL（用于界面显示）
    currentImageUrl = URL.createObjectURL(file);

    showToast('图片已选择', '图片已准备就绪，点击发送将自动上传到云端');
}

// 移除图片
function removeImage() {
    $('#uploadedImagePreview').hide();
    $('#imageInput').val('');

    // 清理URL对象，避免内存泄漏
    if (currentImageUrl) {
        URL.revokeObjectURL(currentImageUrl);
    }

    currentImageUrl = null;
    currentImageFile = null;
    updateSendButton();
}

// 更新发送按钮状态
function updateSendButton() {
    const hasText = $('#messageInput').val().trim().length > 0;
    const hasImage = currentImageUrl !== null;
    const canSend = (hasText || hasImage) && !isProcessing;

    $('#sendBtn').prop('disabled', !canSend);
}

// 发送消息
async function sendMessage() {
    if (isProcessing) return;

    const text = $('#messageInput').val().trim();
    const hasImage = currentImageUrl !== null;

    if (!text && !hasImage) {
        showToast('输入错误', '请输入文字或上传图片！', 'error');
        return;
    }

    // 确保有当前聊天
    if (!currentChatId) {
        createNewChat();
    }

    isProcessing = true;
    updateSendButton();

    let finalImageUrl = null;

    try {
        // 如果有图片，先上传到腾讯云COS
        if (hasImage) {
            showToast('上传中', '正在上传图片到云端...', 'info');
            finalImageUrl = await uploadImageToCOS();
            showToast('上传成功', '图片已成功上传到云端');
        }

        // 添加用户消息
        const userContent = text || '请分析这张图片';
        appendMessage('user', userContent, finalImageUrl);

        // 保存用户消息
        chats[currentChatId].messages.push({
            role: 'user',
            content: userContent,
            imageUrl: finalImageUrl
        });

        // 更新聊天标题
        if (chats[currentChatId].title === '新对话') {
            chats[currentChatId].title = text.substring(0, 20) + (text.length > 20 ? '...' : '');
            renderChatList();
        }

        // 清空输入
        clearInput();

        // 调用API
        const outputMode = $('input[name="outputMode"]:checked').val();
        if (outputMode === 'streaming') {
            await sendStreamingRequest(text, finalImageUrl);
        } else {
            await sendBlockingRequest(text, finalImageUrl);
        }
    } catch (error) {
        console.error('处理请求失败:', error);
        appendMessage('assistant', '抱歉，处理您的请求时出现了错误：' + error.message);
        showToast('处理失败', error.message, 'error');
    }

    isProcessing = false;
    updateSendButton();
    saveChats();
}

// 发送阻塞请求
async function sendBlockingRequest(text, imageUrl) {
    const apiKey = getApiKey();
    const baseUrl = getBaseUrl();
    const model = $('#modelSelect').val();

    // 添加加载消息
    const $loadingMessage = $(`
        <div class="message assistant">
            <div class="message-avatar"><i class="fas fa-robot"></i></div>
            <div class="message-content">
                <div class="message-text">正在分析<span class="loading-dots"></span></div>
            </div>
        </div>
    `);
    $('#chatMessages').append($loadingMessage);
    scrollToBottom();

    const messages = [{
        role: 'user',
        content: []
    }];

    if (text) {
        messages[0].content.push({
            type: 'text',
            text: text
        });
    }

    if (imageUrl) {
        messages[0].content.push({
            type: 'image_url',
            image_url: { url: imageUrl }
        });
    }

    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
            model: model,
            messages: messages,
            max_tokens: 1000,
            stream: false
        })
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // 移除加载消息
    $loadingMessage.remove();

    // 添加AI回复
    appendMessage('assistant', content);

    // 保存AI消息
    chats[currentChatId].messages.push({
        role: 'assistant',
        content: content
    });
}

// 发送流式请求
async function sendStreamingRequest(text, imageUrl) {
    const apiKey = getApiKey();
    const baseUrl = getBaseUrl();
    const model = $('#modelSelect').val();

    const messages = [{
        role: 'user',
        content: []
    }];

    if (text) {
        messages[0].content.push({
            type: 'text',
            text: text
        });
    }

    if (imageUrl) {
        messages[0].content.push({
            type: 'image_url',
            image_url: { url: imageUrl }
        });
    }

    // 添加流式消息容器
    const $streamingMessage = $(`
        <div class="message assistant">
            <div class="message-avatar"><i class="fas fa-robot"></i></div>
            <div class="message-content">
                <div class="message-text"><span class="streaming-cursor"></span></div>
            </div>
        </div>
    `);
    $('#chatMessages').append($streamingMessage);
    scrollToBottom();

    const response = await fetch(`${baseUrl}/v1/chat/completions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
            model: model,
            messages: messages,
            max_tokens: 1000,
            stream: true
        })
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let content = '';

    try {
        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') {
                        // 移除光标
                        $streamingMessage.find('.streaming-cursor').remove();
                        break;
                    }

                    try {
                        const parsed = JSON.parse(data);
                        const delta = parsed.choices[0].delta;
                        if (delta.content) {
                            content += delta.content;
                            // 更新消息内容
                            $streamingMessage.find('.message-text').html(
                                formatMessage(content) + '<span class="streaming-cursor"></span>'
                            );
                            scrollToBottom();
                        }
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
            }
        }
    } finally {
        reader.releaseLock();
    }

    // 保存AI消息
    chats[currentChatId].messages.push({
        role: 'assistant',
        content: content
    });
}

// 格式化消息内容
function formatMessage(content) {
    // 简单的markdown渲染
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>');
}

// 清空输入
function clearInput() {
    $('#messageInput').val('');
    removeImage();
    updateSendButton();
    updateCharCount();
}

// 滚动到底部
function scrollToBottom() {
    const $chatMessages = $('#chatMessages');
    $chatMessages.scrollTop($chatMessages[0].scrollHeight);
}

// 保存聊天记录
function saveChats() {
    localStorage.setItem('perception_chats', JSON.stringify(chats));
}

// 上传图片到腾讯云COS
async function uploadImageToCOS() {
    if (!currentImageFile) {
        throw new Error('没有选择图片文件');
    }

    try {
        // 1. 获取上传参数
        const uploadParams = await getUploadParams();

        // 2. 上传图片到腾讯云COS
        await uploadFileToCOS(currentImageFile, uploadParams.url);

        // 3. 构建最终的图片URL
        const finalImageUrl = `https://cos-cdn-v1.qj-robots.com/${uploadParams.key}`;

        return finalImageUrl;
    } catch (error) {
        console.error('图片上传失败:', error);
        throw new Error(`图片上传失败: ${error.message}`);
    }
}

// 获取上传参数
async function getUploadParams() {
    // 根据当前域名自动判断上传参数接口地址
    let uploadParamsUrl;
    if (window.location.hostname === 'jwd.vooice.tech') {
        uploadParamsUrl = 'https://jwd.vooice.tech/open-api/open-apis/base/upload/params';
    } else {
        uploadParamsUrl = 'http://localhost:9999/open-apis/base/upload/params';
    }

    const response = await fetch(uploadParamsUrl, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`获取上传参数失败: HTTP ${response.status}`);
    }

    const result = await response.json();

    if (result.code !== 0) {
        throw new Error(`获取上传参数失败: ${result.message}`);
    }

    return result.data;
}

// 上传文件到腾讯云COS
async function uploadFileToCOS(file, uploadUrl) {
    // 直接发送PUT请求到返回的URL，请求体为文件内容
    const response = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
            'Content-Type': file.type
        },
        body: file
    });

    // 腾讯云COS上传成功返回200状态码
    if (response.status !== 200) {
        throw new Error(`文件上传失败: HTTP ${response.status}`);
    }

    return true;
}

// 新增功能函数

// 获取API配置
function getApiKey() {
    return $('#apiKey').val() || 'sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc';
}

function getBaseUrl() {
    const inputValue = $('#baseUrl').val();
    if (inputValue) {
        return inputValue;
    }

    // 根据当前域名自动判断API地址
    if (window.location.hostname === 'jwd.vooice.tech') {
        return 'https://jwd.vooice.tech/openai';
    } else {
        return 'http://localhost:8000';
    }
}

// 更新字符计数
function updateCharCount() {
    const text = $('#messageInput').val();
    const count = text.length;
    $('#charCount').text(count);

    if (count > 1800) {
        $('#charCount').addClass('text-warning');
    } else if (count > 2000) {
        $('#charCount').addClass('text-danger').removeClass('text-warning');
    } else {
        $('#charCount').removeClass('text-warning text-danger');
    }
}

// 获取时间差
function getTimeAgo(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return date.toLocaleDateString();
}

// 主题切换
function toggleTheme() {
    const html = document.documentElement;
    const currentTheme = html.getAttribute('data-bs-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    html.setAttribute('data-bs-theme', newTheme);
    localStorage.setItem('theme', newTheme);

    // 更新按钮文本
    const $toggle = $('#themeToggle');
    if (newTheme === 'dark') {
        $toggle.html('<i class="bi bi-sun"></i> 浅色模式');
    } else {
        $toggle.html('<i class="bi bi-moon"></i> 深色模式');
    }

    showToast('主题已切换', `已切换到${newTheme === 'dark' ? '深色' : '浅色'}模式`);
}



// 切换API Key可见性
function toggleApiKeyVisibility() {
    const $input = $('#apiKey');
    const $btn = $('#toggleApiKey');

    if ($input.attr('type') === 'password') {
        $input.attr('type', 'text');
        $btn.html('<i class="bi bi-eye-slash"></i>');
    } else {
        $input.attr('type', 'password');
        $btn.html('<i class="bi bi-eye"></i>');
    }
}

// 清除聊天历史
function clearChatHistory() {
    if (confirm('确定要清除所有聊天记录吗？此操作不可撤销。')) {
        chats = {};
        currentChatId = null;
        localStorage.removeItem('perception_chats');
        renderChatList();
        renderMessages();
        showToast('历史已清除', '所有聊天记录已被清除');
    }
}

// 导出当前聊天
function exportCurrentChat() {
    if (!currentChatId || !chats[currentChatId]) {
        showToast('导出失败', '没有可导出的聊天记录', 'error');
        return;
    }

    const chat = chats[currentChatId];
    const exportData = {
        title: chat.title,
        createdAt: chat.createdAt,
        messages: chat.messages
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat_${chat.title}_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showToast('导出成功', '聊天记录已导出到文件');
}

// 分享当前聊天
function shareCurrentChat() {
    if (!currentChatId || !chats[currentChatId]) {
        showToast('分享失败', '没有可分享的聊天记录', 'error');
        return;
    }

    // 生成分享链接（模拟）
    const shareUrl = `${window.location.origin}/share/${currentChatId}`;

    if (navigator.share) {
        navigator.share({
            title: '感知API聊天记录',
            text: `查看我与感知API的对话：${chats[currentChatId].title}`,
            url: shareUrl
        });
    } else {
        // 复制到剪贴板
        navigator.clipboard.writeText(shareUrl).then(() => {
            showToast('链接已复制', '分享链接已复制到剪贴板');
        });
    }
}

// 文件附件（占位符）
function handleFileAttach() {
    showToast('功能开发中', '文件附件功能正在开发中', 'info');
}

// 语音输入（占位符）
function handleVoiceInput() {
    showToast('功能开发中', '语音输入功能正在开发中', 'info');
}

// 表情选择器（占位符）
function showEmojiPicker() {
    showToast('功能开发中', '表情选择器正在开发中', 'info');
}

// 显示Toast通知
function showToast(title, message, type = 'success') {
    const toastId = 'toast_' + Date.now();
    const iconClass = {
        success: 'bi-check-circle-fill text-success',
        error: 'bi-x-circle-fill text-danger',
        warning: 'bi-exclamation-triangle-fill text-warning',
        info: 'bi-info-circle-fill text-info'
    }[type] || 'bi-check-circle-fill text-success';

    const toastHtml = `
        <div class="toast" id="${toastId}" role="alert">
            <div class="toast-header">
                <i class="bi ${iconClass} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">${message}</div>
        </div>
    `;

    $('#toastContainer').append(toastHtml);
    const toast = new bootstrap.Toast(document.getElementById(toastId));
    toast.show();

    // 自动清理
    setTimeout(() => {
        $(`#${toastId}`).remove();
    }, 5000);
}
