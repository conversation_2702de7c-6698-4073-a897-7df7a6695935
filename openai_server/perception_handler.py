#!/usr/bin/env python3
"""
感知API处理器 - 统一处理流式和非流式请求
"""

import json
import time
import uuid
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union
from py_qj_robots.perception import Perception
from function_call_manager import FunctionCallManager


class PerceptionHandler:
    """感知API处理器 - 统一处理流式和非流式请求"""
    
    def __init__(self, logger):
        self.logger = logger
        self.function_call_manager = FunctionCallManager(logger)
        self.function_map = {
            "check_image": self._call_check_image,
            "split_image": self._call_split_image,
            "props_describe": self._call_props_describe,
            "angle_prediction": self._call_angle_prediction,
            "key_point_prediction": self._call_key_point_prediction,
            "grab_point_prediction": self._call_grab_point_prediction,
            "full_perception": self._call_full_perception
        }
    
    def determine_function(self, text_content: str, functions: Optional[List[Dict]] = None, 
                          function_call: Optional[Union[str, Dict]] = None) -> Tuple[str, Dict]:
        """智能确定要调用的函数
        
        Args:
            text_content: 用户输入的文本内容
            functions: 可用函数列表（OpenAI格式）
            function_call: 指定的函数调用（OpenAI格式）
            
        Returns:
            Tuple[函数名, 函数参数]
        """
        # 如果明确指定了function_call，使用指定的函数
        if function_call:
            if isinstance(function_call, dict):
                function_name = function_call.get("name", "check_image")
                # 从functions中提取参数
                for func in (functions or []):
                    if func.get("name") == function_name:
                        return function_name, func.get("parameters", {})
                return function_name, {}
            elif isinstance(function_call, str):
                return function_call, {}
        
        # 根据文本内容智能选择函数
        text_lower = text_content.lower()
        
        # 关键词映射 - 按优先级排序，更具体的关键词优先
        function_keywords = {
            "angle_prediction": ["angle", "rotation", "orientation", "direction", "rotate"],
            "key_point_prediction": ["keypoint", "key point", "landmark", "joints", "skeleton"],
            "grab_point_prediction": ["grab", "grasp", "pick", "hold", "manipulation", "robot"],
            "split_image": ["split", "segment", "separate", "divide", "cut"],
            "full_perception": ["full", "complete", "comprehensive", "everything", "all", "analyze"],
            "props_describe": ["describe", "property", "detail", "characteristics", "what is"],
        }
        
        # 计算匹配分数
        scores = {}
        for func_name, keywords in function_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                scores[func_name] = score
        
        # 返回得分最高的函数，默认为check_image
        if scores:
            best_function = max(scores.items(), key=lambda x: x[1])[0]
            self.logger.info(f"Auto-selected function: {best_function} (score: {scores[best_function]})")
            return best_function, {}
        else:
            return "check_image", {}
    
    async def process_request(self, perception: Perception, image_url: str, 
                            text_content: str, function_name: str, 
                            function_params: Dict) -> str:
        """统一的请求处理逻辑
        
        Args:
            perception: 感知API实例
            image_url: 图片URL
            text_content: 文本内容
            function_name: 要调用的函数名
            function_params: 函数参数
            
        Returns:
            处理结果字符串
        """
        try:
            self.logger.info(f"Processing perception request: {function_name}")
            
            # 提取对象名称
            object_names = self._extract_object_names_from_text(text_content)
            if not object_names:
                object_names = ["car", "person", "object"]  # 默认对象
            
            self.logger.info(f"Detected objects: {object_names}")
            
            # 调用对应的感知函数
            if function_name in self.function_map:
                result = await self.function_map[function_name](
                    perception, image_url, object_names, function_params
                )
            else:
                raise ValueError(f"Unknown function: {function_name}")
            
            # 格式化结果
            content_parts = self._parse_perception_result(result, text_content, function_name)
            return "".join(content_parts)
            
        except Exception as e:
            self.logger.error(f"Perception processing error: {e}")
            raise
    
    async def _call_check_image(self, perception: Perception, image_url: str, 
                              object_names: List[str], params: Dict) -> Any:
        """调用check_image函数"""
        return perception.check_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    async def _call_split_image(self, perception: Perception, image_url: str, 
                              object_names: List[str], params: Dict) -> Any:
        """调用split_image函数"""
        return perception.split_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    async def _call_props_describe(self, perception: Perception, image_url: str, 
                                 object_names: List[str], params: Dict) -> Any:
        """调用props_describe函数"""
        questions = params.get("questions", ["Describe this object in detail"])
        return perception.props_describe(
            image_type="2D",
            image_url=image_url,
            object_names=object_names,
            questions=questions
        )
    
    async def _call_angle_prediction(self, perception: Perception, image_url: str, 
                                   object_names: List[str], params: Dict) -> Any:
        """调用angle_prediction函数"""
        return perception.angle_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    async def _call_key_point_prediction(self, perception: Perception, image_url: str, 
                                       object_names: List[str], params: Dict) -> Any:
        """调用key_point_prediction函数"""
        return perception.key_point_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    async def _call_grab_point_prediction(self, perception: Perception, image_url: str, 
                                        object_names: List[str], params: Dict) -> Any:
        """调用grab_point_prediction函数"""
        return perception.grab_point_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    async def _call_full_perception(self, perception: Perception, image_url: str, 
                                  object_names: List[str], params: Dict) -> Any:
        """调用full_perception函数"""
        questions = params.get("questions", ["Provide comprehensive analysis"])
        return perception.full_perception(
            image_type="2D",
            image_url=image_url,
            object_names=object_names,
            questions=questions
        )
    
    def _extract_object_names_from_text(self, text: str) -> List[str]:
        """从文本中提取对象名称"""
        # 常见对象关键词
        common_objects = [
            "car", "vehicle", "automobile", "truck", "bus",
            "person", "people", "human", "man", "woman", "child",
            "building", "house", "structure",
            "tree", "plant", "flower",
            "animal", "dog", "cat", "bird",
            "object", "thing", "item"
        ]
        
        text_lower = text.lower()
        found_objects = []
        
        for obj in common_objects:
            if obj in text_lower:
                found_objects.append(obj)
        
        # 去重并保持顺序
        return list(dict.fromkeys(found_objects))

    def get_function_definitions(self) -> List[Dict]:
        """获取标准化的函数定义"""
        return self.function_call_manager.function_definitions

    def should_call_function(self, messages: List[Dict], functions: Optional[List[Dict]] = None,
                           function_call: Optional[Union[str, Dict]] = None):
        """判断是否需要调用函数"""
        return self.function_call_manager.should_call_function(messages, functions, function_call)

    def validate_function_call(self, function_name: str, arguments: Dict,
                             functions: Optional[List[Dict]] = None) -> Tuple[bool, str]:
        """验证函数调用"""
        return self.function_call_manager.validate_function_call(function_name, arguments, functions)

    async def execute_function_call(self, perception: Perception, function_name: str,
                                  arguments: Dict) -> Any:
        """执行函数调用

        Args:
            perception: 感知API实例
            function_name: 函数名
            arguments: 函数参数

        Returns:
            函数执行结果
        """
        try:
            self.logger.info(f"Executing function call: {function_name} with args: {arguments}")

            # 验证函数调用
            is_valid, error_msg = self.validate_function_call(function_name, arguments)
            if not is_valid:
                raise ValueError(f"Invalid function call: {error_msg}")

            # 调用对应的感知函数
            if function_name in self.function_map:
                result = await self.function_map[function_name](
                    perception,
                    arguments.get("image_url", ""),
                    arguments.get("object_names", []),
                    arguments
                )
                return result
            else:
                raise ValueError(f"Unknown function: {function_name}")

        except Exception as e:
            self.logger.error(f"Function call execution error: {e}")
            raise

    def format_function_result(self, result: Any, function_name: str,
                             original_request: str = "") -> str:
        """格式化函数执行结果为用户友好的文本

        Args:
            result: 函数执行结果
            function_name: 函数名
            original_request: 用户的原始请求

        Returns:
            格式化后的结果文本
        """
        try:
            # 根据函数类型格式化结果
            if function_name == "check_image":
                return self._format_check_image_result_for_user(result, original_request)
            elif function_name == "split_image":
                return self._format_split_image_result_for_user(result, original_request)
            elif function_name == "props_describe":
                return self._format_props_describe_result_for_user(result, original_request)
            elif function_name == "angle_prediction":
                return self._format_angle_prediction_result_for_user(result, original_request)
            elif function_name == "key_point_prediction":
                return self._format_key_point_prediction_result_for_user(result, original_request)
            elif function_name == "grab_point_prediction":
                return self._format_grab_point_prediction_result_for_user(result, original_request)
            elif function_name == "full_perception":
                return self._format_full_perception_result_for_user(result, original_request)
            else:
                # 默认格式化
                return f"✅ {function_name}执行完成\n\n结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"

        except Exception as e:
            self.logger.error(f"Result formatting error: {e}")
            return f"⚠️ 结果格式化出错，原始结果：{str(result)}"
    
    def _parse_perception_result(self, result: Any, text_content: str, function_name: str) -> List[str]:
        """解析感知结果
        
        Args:
            result: 感知API返回的结果
            text_content: 原始文本内容
            function_name: 调用的函数名
            
        Returns:
            格式化后的内容列表
        """
        try:
            # 根据函数类型格式化结果
            if function_name == "check_image":
                return self._format_check_image_result(result, text_content)
            elif function_name == "split_image":
                return self._format_split_image_result(result, text_content)
            elif function_name == "props_describe":
                return self._format_props_describe_result(result, text_content)
            elif function_name == "angle_prediction":
                return self._format_angle_prediction_result(result, text_content)
            elif function_name == "key_point_prediction":
                return self._format_key_point_prediction_result(result, text_content)
            elif function_name == "grab_point_prediction":
                return self._format_grab_point_prediction_result(result, text_content)
            elif function_name == "full_perception":
                return self._format_full_perception_result(result, text_content)
            else:
                # 默认格式化
                return [f"感知分析结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
                
        except Exception as e:
            self.logger.error(f"Result parsing error: {e}")
            return [f"结果解析错误：{str(e)}"]
    
    def _format_check_image_result(self, result: Any, text_content: str) -> List[str]:
        """格式化对象检测结果"""
        if isinstance(result, dict):
            return [f"🔍 对象检测结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"🔍 对象检测结果：{str(result)}"]
    
    def _format_split_image_result(self, result: Any, text_content: str) -> List[str]:
        """格式化图像分割结果"""
        if isinstance(result, dict):
            return [f"✂️ 图像分割结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"✂️ 图像分割结果：{str(result)}"]
    
    def _format_props_describe_result(self, result: Any, text_content: str) -> List[str]:
        """格式化属性描述结果"""
        if isinstance(result, dict):
            return [f"📝 属性描述结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"📝 属性描述结果：{str(result)}"]
    
    def _format_angle_prediction_result(self, result: Any, text_content: str) -> List[str]:
        """格式化角度预测结果"""
        if isinstance(result, dict):
            return [f"📐 角度预测结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"📐 角度预测结果：{str(result)}"]
    
    def _format_key_point_prediction_result(self, result: Any, text_content: str) -> List[str]:
        """格式化关键点预测结果"""
        if isinstance(result, dict):
            return [f"🎯 关键点预测结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"🎯 关键点预测结果：{str(result)}"]
    
    def _format_grab_point_prediction_result(self, result: Any, text_content: str) -> List[str]:
        """格式化抓取点预测结果"""
        if isinstance(result, dict):
            return [f"🤖 抓取点预测结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"🤖 抓取点预测结果：{str(result)}"]
    
    def _format_full_perception_result(self, result: Any, text_content: str) -> List[str]:
        """格式化全面感知结果"""
        if isinstance(result, dict):
            return [f"🧠 全面感知分析结果：\n{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"🧠 全面感知分析结果：{str(result)}"]

    # 用户友好的结果格式化方法 (用于Function Calling)
    def _format_check_image_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化对象检测结果"""
        if isinstance(result, dict):
            response_parts = ["🔍 **图像对象检测结果**\n"]

            # 检查任务状态
            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 检测完成！\n")

                # 解析检测结果
                task_result = result.get('taskResult', {})
                labels = task_result.get('labels', [])
                scores = task_result.get('scores', [])
                boxes = task_result.get('boxes', [])

                if labels:
                    response_parts.append("**检测到的对象：**\n")
                    object_counts = {}
                    for label in labels:
                        object_counts[label] = object_counts.get(label, 0) + 1

                    for obj, count in object_counts.items():
                        response_parts.append(f"• {obj}：{count}个\n")

                    if scores:
                        avg_confidence = sum(scores) / len(scores)
                        response_parts.append(f"\n📊 平均检测置信度：{avg_confidence:.1%}")
                else:
                    response_parts.append("未检测到指定的对象。")
            else:
                response_parts.append(f"⚠️ 检测状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"🔍 对象检测结果：{str(result)}"

    def _format_split_image_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化图像分割结果"""
        if isinstance(result, dict):
            response_parts = ["✂️ **图像分割结果**\n"]

            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 分割完成！\n")
                response_parts.append("图像已成功分割，不同对象已被分离识别。")
            else:
                response_parts.append(f"⚠️ 分割状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"✂️ 图像分割结果：{str(result)}"

    def _format_props_describe_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化属性描述结果"""
        if isinstance(result, dict):
            response_parts = ["📝 **对象属性描述**\n"]

            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 描述完成！\n")

                # 尝试提取描述内容
                task_result = result.get('taskResult', {})
                if isinstance(task_result, dict):
                    for key, value in task_result.items():
                        response_parts.append(f"**{key}**：{value}\n")
                else:
                    response_parts.append(f"描述内容：{str(task_result)}")
            else:
                response_parts.append(f"⚠️ 描述状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"📝 对象属性描述：{str(result)}"

    def _format_angle_prediction_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化角度预测结果"""
        if isinstance(result, dict):
            response_parts = ["📐 **角度预测结果**\n"]

            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 角度分析完成！\n")

                task_result = result.get('taskResult', {})
                if isinstance(task_result, dict):
                    for obj, angle_info in task_result.items():
                        response_parts.append(f"**{obj}**：{angle_info}\n")
                else:
                    response_parts.append(f"角度信息：{str(task_result)}")
            else:
                response_parts.append(f"⚠️ 角度分析状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"📐 角度预测结果：{str(result)}"

    def _format_key_point_prediction_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化关键点预测结果"""
        if isinstance(result, dict):
            response_parts = ["🎯 **关键点预测结果**\n"]

            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 关键点检测完成！\n")

                task_result = result.get('taskResult', {})
                if isinstance(task_result, dict):
                    for obj, keypoints in task_result.items():
                        response_parts.append(f"**{obj}的关键点**：\n")
                        if isinstance(keypoints, list):
                            for i, point in enumerate(keypoints):
                                response_parts.append(f"  • 关键点{i+1}：{point}\n")
                        else:
                            response_parts.append(f"  {keypoints}\n")
                else:
                    response_parts.append(f"关键点信息：{str(task_result)}")
            else:
                response_parts.append(f"⚠️ 关键点检测状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"🎯 关键点预测结果：{str(result)}"

    def _format_grab_point_prediction_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化抓取点预测结果"""
        if isinstance(result, dict):
            response_parts = ["🤖 **机器人抓取点预测**\n"]

            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 抓取点分析完成！\n")

                task_result = result.get('taskResult', {})
                if isinstance(task_result, dict):
                    for obj, grab_info in task_result.items():
                        response_parts.append(f"**{obj}的最佳抓取点**：\n")
                        if isinstance(grab_info, dict):
                            for key, value in grab_info.items():
                                response_parts.append(f"  • {key}：{value}\n")
                        else:
                            response_parts.append(f"  {grab_info}\n")
                else:
                    response_parts.append(f"抓取点信息：{str(task_result)}")
            else:
                response_parts.append(f"⚠️ 抓取点分析状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"🤖 抓取点预测结果：{str(result)}"

    def _format_full_perception_result_for_user(self, result: Any, original_request: str) -> str:
        """为用户格式化全面感知结果"""
        if isinstance(result, dict):
            response_parts = ["🧠 **全面感知分析报告**\n"]

            task_status = result.get('taskStatus', '')
            if task_status == 'DONE':
                response_parts.append("✅ 全面分析完成！\n")

                task_result = result.get('taskResult', {})
                if isinstance(task_result, dict):
                    response_parts.append("**分析结果：**\n")
                    for category, info in task_result.items():
                        response_parts.append(f"\n**{category}**：\n")
                        if isinstance(info, dict):
                            for key, value in info.items():
                                response_parts.append(f"  • {key}：{value}\n")
                        elif isinstance(info, list):
                            for item in info:
                                response_parts.append(f"  • {item}\n")
                        else:
                            response_parts.append(f"  {info}\n")
                else:
                    response_parts.append(f"分析内容：{str(task_result)}")
            else:
                response_parts.append(f"⚠️ 分析状态：{task_status}")

            return "".join(response_parts)
        else:
            return f"🧠 全面感知分析：{str(result)}"
