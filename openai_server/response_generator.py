#!/usr/bin/env python3
"""
响应生成器 - 统一处理流式和非流式响应
"""

import json
import time
import uuid
import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator
from fastapi.responses import StreamingResponse
from py_qj_robots.perception import Perception
from perception_handler import Per<PERSON><PERSON>andler
from function_call_manager import FunctionCallDecision


class ResponseGenerator:
    """响应生成器 - 统一处理流式和非流式响应"""
    
    def __init__(self, perception_handler: PerceptionHandler, logger):
        self.perception_handler = perception_handler
        self.logger = logger
    
    def create_completion_id(self) -> str:
        """生成唯一的完成ID"""
        return f"chatcmpl-{uuid.uuid4().hex[:29]}"

    async def create_function_calling_response(self, request, perception: Perception,
                                             messages: List[Dict]) -> Dict:
        """创建Function Calling响应

        Args:
            request: 请求对象
            perception: 感知API实例
            messages: 消息列表

        Returns:
            OpenAI格式的响应
        """
        try:
            # 判断是否需要调用函数
            decision = self.perception_handler.should_call_function(
                messages,
                getattr(request, 'functions', None),
                getattr(request, 'function_call', None)
            )

            self.logger.info(f"Function call decision: {decision}")

            if decision.should_call:
                # 需要调用函数，返回function_call响应
                return await self._create_function_call_response(
                    request, decision.function_name, decision.arguments
                )
            else:
                # 不需要调用函数，生成普通文本响应
                return await self._create_text_response(
                    request, perception, messages, decision.reason
                )

        except Exception as e:
            self.logger.error(f"Function calling response error: {e}")
            raise

    async def create_function_result_response(self, request, perception: Perception,
                                            messages: List[Dict]) -> Dict:
        """处理函数执行结果并生成最终响应

        Args:
            request: 请求对象
            perception: 感知API实例
            messages: 包含函数调用和结果的消息列表

        Returns:
            最终的文本响应
        """
        try:
            # 查找最后的函数调用和结果
            function_call_msg = None
            function_result_msg = None

            for msg in reversed(messages):
                if msg.get("role") == "assistant" and msg.get("function_call"):
                    function_call_msg = msg
                elif msg.get("role") == "function":
                    function_result_msg = msg
                    break

            if not function_call_msg or not function_result_msg:
                raise ValueError("Missing function call or result message")

            # 提取函数信息
            function_name = function_call_msg["function_call"]["name"]
            function_result = function_result_msg["content"]

            # 获取原始用户请求
            original_request = ""
            for msg in messages:
                if msg.get("role") == "user":
                    content = msg.get("content", "")
                    if isinstance(content, list):
                        for item in content:
                            if item.get("type") == "text":
                                original_request += item.get("text", "") + " "
                    elif isinstance(content, str):
                        original_request += content + " "

            # 格式化函数结果为用户友好的文本
            try:
                # 尝试解析JSON结果
                parsed_result = json.loads(function_result) if isinstance(function_result, str) else function_result
                formatted_response = self.perception_handler.format_function_result(
                    parsed_result, function_name, original_request.strip()
                )
            except json.JSONDecodeError:
                # 如果不是JSON，直接使用原始结果
                formatted_response = self.perception_handler.format_function_result(
                    function_result, function_name, original_request.strip()
                )

            # 创建最终响应
            completion_id = self.create_completion_id()
            return {
                "id": completion_id,
                "object": "chat.completion",
                "created": int(time.time()),
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": formatted_response
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": len(str(messages).split()),
                    "completion_tokens": len(formatted_response.split()),
                    "total_tokens": len(str(messages).split()) + len(formatted_response.split())
                }
            }

        except Exception as e:
            self.logger.error(f"Function result response error: {e}")
            raise
    
    async def create_streaming_response(self, request, perception: Perception, 
                                      image_url: str, text_content: str,
                                      function_name: str, function_params: Dict) -> StreamingResponse:
        """创建流式响应"""
        async def generate_stream() -> AsyncGenerator[str, None]:
            try:
                completion_id = self.create_completion_id()
                current_time = int(time.time())
                
                # 发送开始chunk
                yield self._create_chunk_data(
                    completion_id, request.model, current_time, 
                    {"role": "assistant", "content": ""}
                )
                
                # 发送状态信息
                status_messages = [
                    "🔍 正在分析图片...",
                    f"\n📊 调用{function_name}功能...",
                    "\n🤖 处理感知结果..."
                ]
                
                for status_msg in status_messages:
                    yield self._create_chunk_data(
                        completion_id, request.model, current_time,
                        {"content": status_msg}
                    )
                    await asyncio.sleep(0.5)
                
                # 处理感知请求
                response_content = await self.perception_handler.process_request(
                    perception, image_url, text_content, 
                    function_name, function_params
                )
                
                # 流式发送内容
                async for chunk in self._stream_content(
                    completion_id, request.model, current_time, response_content
                ):
                    yield chunk
                
                # 发送结束chunk
                yield self._create_chunk_data(
                    completion_id, request.model, current_time,
                    {}, finish_reason="stop"
                )
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                self.logger.error(f"Streaming error: {e}")
                yield self._create_error_chunk(request.model, str(e))
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive", 
                "Content-Type": "text/event-stream"
            }
        )
    
    async def create_non_streaming_response(self, request, perception: Perception,
                                          image_url: str, text_content: str,
                                          function_name: str, function_params: Dict) -> Dict:
        """创建非流式响应"""
        try:
            completion_id = self.create_completion_id()
            current_time = int(time.time())
            
            # 处理感知请求
            response_content = await self.perception_handler.process_request(
                perception, image_url, text_content,
                function_name, function_params
            )
            
            # 创建响应
            return {
                "id": completion_id,
                "object": "chat.completion",
                "created": current_time,
                "model": request.model,
                "choices": [{
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_content
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": len(text_content.split()),
                    "completion_tokens": len(response_content.split()),
                    "total_tokens": len(text_content.split()) + len(response_content.split())
                }
            }
            
        except Exception as e:
            self.logger.error(f"Non-streaming error: {e}")
            raise
    
    def _create_chunk_data(self, completion_id: str, model: str, created_time: int, 
                          delta: Dict, finish_reason: Optional[str] = None) -> str:
        """创建chunk数据"""
        chunk = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created_time,
            "model": model,
            "choices": [{
                "index": 0,
                "delta": delta,
                "finish_reason": finish_reason
            }]
        }
        return f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
    
    async def _stream_content(self, completion_id: str, model: str, 
                            created_time: int, content: str) -> AsyncGenerator[str, None]:
        """流式发送内容"""
        words = content.split()
        chunk_size = 2  # 每次发送2个词
        
        for i in range(0, len(words), chunk_size):
            chunk_words = words[i:i + chunk_size]
            chunk_content = " ".join(chunk_words)
            
            # 在第一个chunk前添加换行
            if i == 0:
                chunk_content = "\n" + chunk_content
            else:
                chunk_content = " " + chunk_content
            
            yield self._create_chunk_data(
                completion_id, model, created_time,
                {"content": chunk_content}
            )
            await asyncio.sleep(0.15)  # 控制发送速度
    
    def _create_error_chunk(self, model: str, error_message: str) -> str:
        """创建错误chunk"""
        error_chunk = {
            "id": f"chatcmpl-{uuid.uuid4().hex[:29]}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {"content": f"\n\n❌ 处理错误: {error_message}"},
                "finish_reason": "stop"
            }]
        }
        return f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"

    async def _create_function_call_response(self, request, function_name: str,
                                           arguments: Dict) -> Dict:
        """创建函数调用响应"""
        completion_id = self.create_completion_id()

        return {
            "id": completion_id,
            "object": "chat.completion",
            "created": int(time.time()),
            "model": request.model,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": None,
                    "function_call": {
                        "name": function_name,
                        "arguments": json.dumps(arguments, ensure_ascii=False)
                    }
                },
                "finish_reason": "function_call"
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }

    async def _create_text_response(self, request, perception: Perception,
                                  messages: List[Dict], reason: str = "") -> Dict:
        """创建普通文本响应"""
        completion_id = self.create_completion_id()

        # 生成简单的文本回复
        response_text = f"我理解您的请求，但当前无法执行函数调用。原因：{reason}" if reason else "我已收到您的消息。"

        return {
            "id": completion_id,
            "object": "chat.completion",
            "created": int(time.time()),
            "model": request.model,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_text
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": len(str(messages).split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(str(messages).split()) + len(response_text.split())
            }
        }


class FunctionCallResponseGenerator:
    """Function Calling 响应生成器"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def create_function_call_response(self, completion_id: str, model: str, 
                                    function_name: str, arguments: Dict) -> Dict:
        """创建函数调用响应"""
        return {
            "id": completion_id,
            "object": "chat.completion",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": None,
                    "function_call": {
                        "name": function_name,
                        "arguments": json.dumps(arguments, ensure_ascii=False)
                    }
                },
                "finish_reason": "function_call"
            }],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
    
    def create_function_call_chunk(self, completion_id: str, model: str, 
                                 function_name: str, arguments: Dict) -> str:
        """创建函数调用chunk（流式）"""
        chunk = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {
                    "role": "assistant",
                    "function_call": {
                        "name": function_name,
                        "arguments": json.dumps(arguments, ensure_ascii=False)
                    }
                },
                "finish_reason": "function_call"
            }]
        }
        return f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"


class StreamingFunctionCallGenerator:
    """流式Function Calling生成器"""

    def __init__(self, perception_handler, logger):
        self.perception_handler = perception_handler
        self.logger = logger

    def create_completion_id(self) -> str:
        """生成唯一的完成ID"""
        return f"chatcmpl-{uuid.uuid4().hex[:29]}"

    async def create_streaming_function_calling_response(self, request, perception: Perception,
                                                       messages: List[Dict]) -> StreamingResponse:
        """创建流式Function Calling响应"""
        async def generate_stream() -> AsyncGenerator[str, None]:
            try:
                completion_id = self.create_completion_id()
                current_time = int(time.time())

                # 判断是否需要调用函数
                decision = self.perception_handler.should_call_function(
                    messages,
                    getattr(request, 'functions', None),
                    getattr(request, 'function_call', None)
                )

                if decision.should_call:
                    # 发送函数调用开始chunk
                    yield self._create_chunk_data(
                        completion_id, request.model, current_time,
                        {"role": "assistant", "content": ""}
                    )

                    # 发送函数调用信息chunk
                    yield self._create_function_call_chunk(
                        completion_id, request.model, current_time,
                        decision.function_name, decision.arguments
                    )

                    # 发送结束chunk
                    yield self._create_chunk_data(
                        completion_id, request.model, current_time,
                        {}, finish_reason="function_call"
                    )
                else:
                    # 发送普通文本响应
                    response_text = f"我理解您的请求，但当前无法执行函数调用。原因：{decision.reason}"

                    yield self._create_chunk_data(
                        completion_id, request.model, current_time,
                        {"role": "assistant", "content": ""}
                    )

                    # 流式发送文本内容
                    words = response_text.split()
                    for i, word in enumerate(words):
                        content = word if i == 0 else " " + word
                        yield self._create_chunk_data(
                            completion_id, request.model, current_time,
                            {"content": content}
                        )
                        await asyncio.sleep(0.05)

                    yield self._create_chunk_data(
                        completion_id, request.model, current_time,
                        {}, finish_reason="stop"
                    )

                yield "data: [DONE]\n\n"

            except Exception as e:
                self.logger.error(f"Streaming function calling error: {e}")
                yield self._create_error_chunk(request.model, str(e))
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )

    def _create_function_call_chunk(self, completion_id: str, model: str,
                                  created_time: int, function_name: str,
                                  arguments: Dict) -> str:
        """创建函数调用chunk"""
        chunk = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created_time,
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {
                    "function_call": {
                        "name": function_name,
                        "arguments": json.dumps(arguments, ensure_ascii=False)
                    }
                },
                "finish_reason": None
            }]
        }
        return f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

    def _create_chunk_data(self, completion_id: str, model: str, created_time: int,
                          delta: Dict, finish_reason: Optional[str] = None) -> str:
        """创建chunk数据"""
        chunk = {
            "id": completion_id,
            "object": "chat.completion.chunk",
            "created": created_time,
            "model": model,
            "choices": [{
                "index": 0,
                "delta": delta,
                "finish_reason": finish_reason
            }]
        }
        return f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

    def _create_error_chunk(self, model: str, error_message: str) -> str:
        """创建错误chunk"""
        error_chunk = {
            "id": f"chatcmpl-{uuid.uuid4().hex[:29]}",
            "object": "chat.completion.chunk",
            "created": int(time.time()),
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {"content": f"\n\n❌ 处理错误: {error_message}"},
                "finish_reason": "stop"
            }]
        }
        return f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
