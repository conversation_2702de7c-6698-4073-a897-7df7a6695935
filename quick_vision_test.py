#!/usr/bin/env python3
"""
快速视觉 API 测试脚本

简化版本，用于快速测试图像识别功能。
"""

import sys
import os
import base64
import mimetypes
from pathlib import Path
import requests

# 导入标准 OpenAI 库
from openai import OpenAI

# API 配置
BASE_URL = "https://jwd.vooice.tech/v1"  # 通过 nginx 转发到本地端口 8000
API_KEY = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"

def encode_image(image_path):
    """编码图片为 base64"""
    if image_path.startswith(('http://', 'https://')):
        # 网络图片
        response = requests.get(image_path)
        image_data = response.content
        mime_type = response.headers.get('content-type', 'image/jpeg')
    else:
        # 本地图片
        with open(image_path, 'rb') as f:
            image_data = f.read()
        mime_type = mimetypes.guess_type(image_path)[0] or 'image/jpeg'
    
    base64_image = base64.b64encode(image_data).decode('utf-8')
    return f"data:{mime_type};base64,{base64_image}"

def test_car_detection(image_path):
    """测试车辆检测"""
    print(f"🔍 分析图片: {image_path}")

    # 处理图片URL
    if image_path.startswith(('http://', 'https://')):
        image_url = image_path
        print("✅ 使用网络图片URL")
    else:
        print("❌ 本地图片不支持，请使用网络图片URL")
        print("   示例: https://example.com/image.jpg")
        return
    
    # 初始化客户端
    client = OpenAI(api_key=API_KEY, base_url=BASE_URL)
    
    # 调用 API
    response = client.chat.completions.create(
        model="qj-perception-v1",
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请分析这张图片，告诉我图中有几辆车，分别是什么颜色？"
                    },
                    {
                        "type": "image_url",
                        "image_url": {"url": image_url}
                    }
                ]
            }
        ],
        max_tokens=500
    )
    
    print("🤖 分析结果:")
    print(response.choices[0].message.content)

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 2:
        print("使用方法: python quick_vision_test.py <image_path>")
        sys.exit(1)
    
    test_car_detection(sys.argv[1])
