#!/usr/bin/env python3
"""
FastAPI server providing OpenAI-compatible REST API for QJ Robots Perception.

This server exposes the perception capabilities through standard OpenAI API endpoints,
making it compatible with existing OpenAI client libraries and applications.

Architecture:
    - Backend API: This server provides pure API services (port 8000)
    - Frontend: Static files served by nginx (front-end/back-end separation)
    - Demo page: https://jwd.vooice.tech/perception/demo.html

Usage:
    python openai_api_server.py

API Endpoints:
    POST /v1/chat/completions    - Chat completions (OpenAI compatible)
    GET  /v1/models             - List available models
    GET  /v1/functions          - List available functions
    GET  /health                - Health check
    GET  /docs                  - API documentation
"""

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Union, Any
import json
import uvicorn
import logging
import os
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from py_qj_robots.openai_api import OpenAIPerceptionAPI
from py_qj_robots.perception import Perception
from utils.token_config import TokenManager
from .perception_handler import PerceptionHandler
from .response_generator import ResponseGenerator, FunctionCallResponseGenerator
# 移除了不再使用的导入: base64, re
import uuid
import time


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建处理器实例
perception_handler = PerceptionHandler(logger)
response_generator = ResponseGenerator(perception_handler, logger)
function_call_generator = FunctionCallResponseGenerator(logger)


# Pydantic models for request/response validation
class ChatMessage(BaseModel):
    role: str = Field(..., description="Role of the message sender")
    content: Union[str, List[Dict[str, Any]]] = Field(..., description="Content of the message - can be string or array for vision")
    name: Optional[str] = Field(None, description="Name of the sender")


class ChatCompletionRequest(BaseModel):
    model: str = Field(default="qj-perception-v1", description="Model to use")
    messages: List[ChatMessage] = Field(..., description="List of messages")
    functions: Optional[List[Dict]] = Field(None, description="Available functions")
    function_call: Optional[Union[str, Dict]] = Field(None, description="Function call behavior")
    temperature: Optional[float] = Field(0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    stream: Optional[bool] = Field(False, description="Whether to stream the response")
    top_p: Optional[float] = Field(1.0, description="Top-p sampling parameter")
    n: Optional[int] = Field(1, description="Number of completions to generate")
    stop: Optional[Union[str, List[str]]] = Field(None, description="Stop sequences")
    presence_penalty: Optional[float] = Field(0.0, description="Presence penalty")
    frequency_penalty: Optional[float] = Field(0.0, description="Frequency penalty")
    logit_bias: Optional[Dict[str, float]] = Field(None, description="Logit bias")
    user: Optional[str] = Field(None, description="User identifier")


class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "qj-robots"


class ModelsResponse(BaseModel):
    object: str = "list"
    data: List[ModelInfo]


# Initialize FastAPI app
app = FastAPI(
    title="QJ Robots Perception API",
    description="OpenAI-compatible API for QJ Robots Perception services",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 静态文件现在由nginx提供服务（前后端分离架构）

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize token manager and security
# 获取项目根目录的配置文件路径
config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "conf", "token_mapping.json")
token_manager = TokenManager(config_file=config_path)
security = HTTPBearer()

def extract_image_and_text(messages: List[Dict]) -> tuple[str, str]:
    """从消息中提取图片和文字

    Args:
        messages: 消息列表

    Returns:
        tuple: (image_data, text_content)

    Raises:
        ValueError: 如果没有找到图片或文字
    """
    image_data = None
    text_content = ""

    for message in messages:
        if message.get("role") == "user":
            content = message.get("content", "")

            if isinstance(content, str):
                # 简单文字消息
                text_content = content
            elif isinstance(content, list):
                # 包含图片和文字的消息
                for item in content:
                    if isinstance(item, dict):
                        if item.get("type") == "text":
                            text_content = item.get("text", "")
                        elif item.get("type") == "image_url":
                            image_url = item.get("image_url", {})
                            image_data = image_url.get("url", "")

    if not image_data:
        raise ValueError("未找到图片数据")

    if not text_content:
        text_content = "请分析这张图片"  # 默认文字

    return image_data, text_content


def parse_perception_result(result: dict, text_content: str) -> List[str]:
    """解析感知API结果并格式化为流式输出内容

    Args:
        result: 感知API返回的结果
        text_content: 用户的原始问题

    Returns:
        List[str]: 格式化后的内容片段列表
    """
    content_parts = []

    # 检查任务状态
    task_status = result.get('taskStatus', '')
    if task_status == 'DONE':
        content_parts.append("🎉 太棒了，我已经帮你分析了图片中的内容！")
        content_parts.append(f"\n\n📝 针对您的问题「{text_content}」，我发现了以下内容：")
    else:
        content_parts.append(f"⚠️ 分析状态：{task_status}")
        return content_parts

    # 解析taskResult
    task_result = result.get('taskResult', {})
    if not task_result:
        content_parts.append("\n\n❌ 未获取到分析结果")
        return content_parts

    # 解析检测到的对象
    labels = task_result.get('labels', [])
    scores = task_result.get('scores', [])
    boxes = task_result.get('boxes', [])

    if labels:
        content_parts.append(f"\n\n🔍 **检测到的对象**：")
        object_counts = {}
        for i, label in enumerate(labels):
            if label in object_counts:
                object_counts[label] += 1
            else:
                object_counts[label] = 1

        for obj, count in object_counts.items():
            if count == 1:
                content_parts.append(f"\n• {obj}（1个）")
            else:
                content_parts.append(f"\n• {obj}（{count}个）")

        # 添加置信度信息
        if scores:
            content_parts.append(f"\n\n📊 **检测置信度**：")
            for i, (label, score) in enumerate(zip(labels, scores)):
                confidence = f"{score:.1%}"
                content_parts.append(f"\n• {label} #{i+1}: {confidence}")

    # 解析边界框信息
    if boxes:
        content_parts.append(f"\n\n📐 **位置信息**：")
        for i, box in enumerate(boxes):
            if len(box) >= 4:
                x, y, w, h = box[:4]
                content_parts.append(f"\n• 对象 #{i+1}: 位置({x:.0f}, {y:.0f}), 大小({w:.0f}×{h:.0f})")

    # 解析裁剪图片
    cropped_images = task_result.get('croppedImagesListBbox', [])
    if cropped_images:
        content_parts.append(f"\n\n🖼️ **检测到的对象图片**：")
        for i, img_url in enumerate(cropped_images):
            if img_url:  # 只显示非空的URL
                content_parts.append(f"\n• 对象 #{i+1}: {img_url}")

    # 解析问答结果
    answers = task_result.get('answers', [])
    if answers:
        content_parts.append(f"\n\n💬 **问答结果**：")
        for i, answer in enumerate(answers):
            if answer:  # 只显示非空答案
                content_parts.append(f"\n• {answer}")

    # 解析角度信息
    angles = task_result.get('angles', [])
    if angles:
        content_parts.append(f"\n\n📐 **角度信息**：")
        for i, angle in enumerate(angles):
            if angle is not None:
                content_parts.append(f"\n• 角度 #{i+1}: {angle}°")

    # 解析3D角度信息
    angles_3d = task_result.get('angles3D', [])
    if angles_3d:
        content_parts.append(f"\n\n🎯 **3D角度信息**：")
        for i, angle_3d in enumerate(angles_3d):
            if angle_3d is not None:
                content_parts.append(f"\n• 3D角度 #{i+1}: {angle_3d}")

    # 解析抓取点信息
    grasps = task_result.get('grasps', [])
    if grasps:
        content_parts.append(f"\n\n🤏 **抓取点信息**：")
        for i, grasp in enumerate(grasps):
            if grasp:
                content_parts.append(f"\n• 抓取点 #{i+1}: {grasp}")

    # 解析关键点信息
    points = task_result.get('points', [])
    if points:
        content_parts.append(f"\n\n📍 **关键点信息**：")
        for i, point in enumerate(points):
            if point:
                content_parts.append(f"\n• 关键点 #{i+1}: {point}")

    # 添加总结
    if labels:
        total_objects = len(labels)
        unique_objects = len(set(labels))
        content_parts.append(f"\n\n📋 **分析总结**：")
        content_parts.append(f"\n• 总共检测到 {total_objects} 个对象")
        content_parts.append(f"\n• 包含 {unique_objects} 种不同类型的对象")

        if scores:
            avg_confidence = sum(scores) / len(scores)
            content_parts.append(f"\n• 平均检测置信度：{avg_confidence:.1%}")

    return content_parts


def extract_object_names_from_text(text: str) -> List[str]:
    """从文字中提取对象名称

    Args:
        text: 用户输入的文字

    Returns:
        List[str]: 提取的对象名称列表
    """
    # 常见对象的关键词映射
    object_keywords = {
        "车": ["car", "vehicle"],
        "汽车": ["car", "vehicle"],
        "人": ["person", "people"],
        "人物": ["person", "people"],
        "动物": ["animal"],
        "狗": ["dog"],
        "猫": ["cat"],
        "建筑": ["building"],
        "房子": ["house", "building"],
        "树": ["tree"],
        "花": ["flower"],
        "食物": ["food"],
        "水果": ["fruit"],
        "苹果": ["apple"],
        "香蕉": ["banana"],
        "物体": ["object"],
        "东西": ["object"],
        "颜色": ["color"],
        "红色": ["red"],
        "蓝色": ["blue"],
        "绿色": ["green"],
        "白色": ["white"],
        "黑色": ["black"]
    }

    extracted_objects = []
    text_lower = text.lower()

    for keyword, objects in object_keywords.items():
        if keyword in text:
            extracted_objects.extend(objects)

    # 英文关键词检测
    english_objects = ["car", "person", "animal", "building", "tree", "flower", "food", "object"]
    for obj in english_objects:
        if obj in text_lower:
            extracted_objects.append(obj)

    # 去重并返回
    return list(set(extracted_objects))


def process_image_data(image_data: str) -> str:
    """处理图片数据，提取URL或转换base64为URL

    Args:
        image_data: base64编码的图片数据或URL

    Returns:
        str: 图片URL

    Raises:
        ValueError: 如果不是有效的URL且不支持base64转换
    """
    if image_data.startswith(("http://", "https://")):
        # URL格式，直接返回
        return image_data
    elif image_data.startswith("data:image/"):
        # base64格式，但感知API需要URL，抛出错误提示
        raise ValueError("感知API需要图片URL，不支持base64格式。请提供图片的HTTP/HTTPS URL。")
    else:
        # 假设是纯base64数据，同样不支持
        raise ValueError("感知API需要图片URL，不支持base64格式。请提供图片的HTTP/HTTPS URL。")


# Global perception API instance (will be created per request with proper credentials)
def get_perception_api(credentials: HTTPAuthorizationCredentials = Depends(security)) -> OpenAIPerceptionAPI:
    """Get perception API instance with proper credentials based on token"""
    token = credentials.credentials

    # Validate token and get credentials
    creds = token_manager.get_credentials(token)
    if not creds:
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )

    app_id, app_secret = creds

    # Set environment variables for this request
    os.environ['QJ_APP_ID'] = app_id
    os.environ['QJ_APP_SECRET'] = app_secret

    # Create perception API instance
    return OpenAIPerceptionAPI(enable_async_mode=True, max_workers=10)


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "QJ Robots Perception API - OpenAI Compatible",
        "version": "1.0.0",
        "endpoints": {
            "chat_completions": "/v1/chat/completions",
            "models": "/v1/models",
            "functions": "/v1/functions",
            "docs": "/docs"
        }
    }


@app.get("/v1/models", response_model=ModelsResponse)
async def list_models():
    """List available models (OpenAI compatible)"""
    return ModelsResponse(
        data=[
            ModelInfo(
                id="qj-perception-v1",
                created=int(datetime.now().timestamp())
            )
        ]
    )


@app.get("/v1/functions")
async def list_functions(perception_api: OpenAIPerceptionAPI = Depends(get_perception_api)):
    """List available perception functions"""
    functions = perception_api.list_functions()
    return {
        "object": "list",
        "data": functions
    }


@app.post("/v1/chat/completions")
async def create_chat_completion(
    request: ChatCompletionRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Create a chat completion with intelligent function calling support"""
    try:
        # 验证token并获取凭据
        token = credentials.credentials
        creds = token_manager.get_credentials(token)
        if not creds:
            raise HTTPException(status_code=401, detail="Invalid API key")

        app_id, app_secret = creds

        # 设置环境变量
        os.environ['QJ_APP_ID'] = app_id
        os.environ['QJ_APP_SECRET'] = app_secret

        # Convert Pydantic models to dicts
        messages = [msg.model_dump() for msg in request.messages]

        # 提取图片和文字
        try:
            image_data, text_content = extract_image_and_text(messages)
            image_url = process_image_data(image_data)
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"图片处理错误: {str(e)}")

        # 创建感知API实例
        perception = Perception()

        # 智能确定要调用的函数
        function_name, function_params = perception_handler.determine_function(
            text_content,
            getattr(request, 'functions', None),
            getattr(request, 'function_call', None)
        )

        logger.info(f"Selected function: {function_name} for request: {text_content[:100]}...")

        # 根据stream参数选择处理方式
        if request.stream:
            return await response_generator.create_streaming_response(
                request, perception, image_url, text_content,
                function_name, function_params
            )
        else:
            return await response_generator.create_non_streaming_response(
                request, perception, image_url, text_content,
                function_name, function_params
            )

    except Exception as e:
        logger.error(f"Chat completion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 保留原有的辅助函数，但标记为已重构
def legacy_streaming_implementation():
    """原有的流式实现 - 已重构到ResponseGenerator中"""
    pass


def legacy_non_streaming_implementation():
    """原有的非流式实现 - 已重构到ResponseGenerator中"""
    pass


# 重构完成 - 旧的实现代码已迁移到 PerceptionHandler 和 ResponseGenerator


@app.get("/health")
async def health_check():
                try:
                    # 生成唯一ID
                    completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
                    current_time = int(time.time())

                    # 发送开始chunk
                    start_chunk = {
                        "id": completion_id,
                        "object": "chat.completion.chunk",
                        "created": current_time,
                        "model": request.model,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {"role": "assistant", "content": ""},
                                "finish_reason": None
                            }
                        ]
                    }
                    yield f"data: {json.dumps(start_chunk, ensure_ascii=False)}\n\n"

                    # 发送处理状态信息
                    status_messages = [
                        "🔍 正在分析图片...",
                        "\n📊 提取图像特征...",
                        "\n🤖 调用感知API..."
                    ]

                    for status_msg in status_messages:
                        status_chunk = {
                            "id": completion_id,
                            "object": "chat.completion.chunk",
                            "created": current_time,
                            "model": request.model,
                            "choices": [
                                {
                                    "index": 0,
                                    "delta": {"content": status_msg},
                                    "finish_reason": None
                                }
                            ]
                        }
                        yield f"data: {json.dumps(status_chunk, ensure_ascii=False)}\n\n"
                        time.sleep(0.5)  # 短暂延迟以显示进度

                    # 从用户文字中提取对象名称
                    object_names = extract_object_names_from_text(text_content)
                    if not object_names:
                        object_names = ["car", "person", "object"]  # 默认对象

                    # 调用感知API
                    result = perception.check_image(
                        image_type="2D",
                        image_url=image_url,
                        object_names=object_names
                    )

                    # 解析和格式化结果
                    content_parts = parse_perception_result(result, text_content)

                    # 流式发送格式化后的内容
                    for part in content_parts:
                        # 将每个部分进一步分词以实现更细粒度的流式效果
                        words = part.split()
                        chunk_size = 2  # 每次发送2个词

                        for i in range(0, len(words), chunk_size):
                            chunk_words = words[i:i + chunk_size]
                            chunk_content = " ".join(chunk_words)
                            if i == 0:
                                chunk_content = "\n" + chunk_content  # 第一个chunk前加换行
                            else:
                                chunk_content = " " + chunk_content

                            content_chunk = {
                                "id": completion_id,
                                "object": "chat.completion.chunk",
                                "created": current_time,
                                "model": request.model,
                                "choices": [
                                    {
                                        "index": 0,
                                        "delta": {"content": chunk_content},
                                        "finish_reason": None
                                    }
                                ]
                            }
                            yield f"data: {json.dumps(content_chunk, ensure_ascii=False)}\n\n"

                            # 添加小延迟以模拟流式效果
                            time.sleep(0.15)

                    # 发送结束chunk
                    end_chunk = {
                        "id": completion_id,
                        "object": "chat.completion.chunk",
                        "created": current_time,
                        "model": request.model,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {},
                                "finish_reason": "stop"
                            }
                        ]
                    }
                    yield f"data: {json.dumps(end_chunk, ensure_ascii=False)}\n\n"

                    # 发送结束标记
                    yield "data: [DONE]\n\n"

                except Exception as e:
                    logger.error(f"Streaming error: {e}")
                    error_chunk = {
                        "id": f"chatcmpl-{uuid.uuid4().hex[:29]}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": request.model,
                        "choices": [
                            {
                                "index": 0,
                                "delta": {"content": f"\n\n❌ 处理错误: {str(e)}"},
                                "finish_reason": "stop"
                            }
                        ]
                    }
                    yield f"data: {json.dumps(error_chunk, ensure_ascii=False)}\n\n"
                    yield "data: [DONE]\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream"
                }
            )
        else:
            # Non-streaming response - 直接调用感知API
            try:
                # 从用户文字中提取对象名称，如果没有则使用通用对象
                object_names = extract_object_names_from_text(text_content)
                if not object_names:
                    object_names = ["car", "person", "object"]  # 默认对象

                # 调用感知API进行图像识别
                result = perception.check_image(
                    image_type="2D",  # 默认2D图像
                    image_url=image_url,
                    object_names=object_names
                )

                # 解析和格式化结果
                content_parts = parse_perception_result(result, text_content)
                response_content = "".join(content_parts)

                # 生成唯一ID
                completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
                current_time = int(time.time())

                # 返回OpenAI格式的响应
                return {
                    "id": completion_id,
                    "object": "chat.completion",
                    "created": current_time,
                    "model": request.model,
                    "choices": [
                        {
                            "index": 0,
                            "message": {
                                "role": "assistant",
                                "content": response_content,
                                "name": None
                            },
                            "finish_reason": "stop"
                        }
                    ],
                    "usage": {
                        "prompt_tokens": len(text_content.split()),
                        "completion_tokens": len(response_content.split()),
                        "total_tokens": len(text_content.split()) + len(response_content.split())
                    }
                }

            except Exception as e:
                logger.error(f"Perception API error: {e}")
                raise HTTPException(status_code=500, detail=f"Perception API error: {str(e)}")
            
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Internal error: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


# Demo页面现在由nginx直接提供服务
# 访问地址: https://jwd.vooice.tech/perception/demo.html


# 图片上传现在通过前端直接调用腾讯云COS接口
# 上传参数接口: https://jwd.vooice.tech/open-api/open-apis/base/upload/params


@app.get("/admin/tokens")
async def list_tokens():
    """List all tokens (admin endpoint - no auth required for demo)"""
    return {
        "tokens": token_manager.list_tokens(),
        "total_count": len(token_manager.token_mapping)
    }


@app.post("/admin/tokens")
async def create_token(app_id: str, app_secret: str, description: str = ""):
    """Create a new token (admin endpoint)"""
    try:
        token = token_manager.add_token(app_id, app_secret, description)
        token_manager.save_config()
        return {
            "token": token,
            "app_id": app_id,
            "description": description,
            "message": "Token created successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create token: {str(e)}")


@app.delete("/admin/tokens/{token}")
async def delete_token(token: str):
    """Delete a token (admin endpoint)"""
    if token_manager.remove_token(token):
        return {"message": "Token deleted successfully"}
    else:
        raise HTTPException(status_code=404, detail="Token not found")


@app.get("/admin/tokens/{token}/validate")
async def validate_token_endpoint(token: str):
    """Validate a specific token (admin endpoint)"""
    is_valid = token_manager.validate_token(token)
    if is_valid:
        creds = token_manager.get_credentials(token)
        return {
            "valid": True,
            "app_id": creds[0] if creds else None,
            "message": "Token is valid"
        }
    else:
        return {
            "valid": False,
            "message": "Token is invalid"
        }


@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests"""
    start_time = datetime.now()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Response: {response.status_code} ({process_time:.3f}s)")
    
    return response


if __name__ == "__main__":
    print("Starting QJ Robots Perception API Server...")
    print("OpenAI-compatible endpoints:")
    print("  POST /v1/chat/completions")
    print("  GET  /v1/models")
    print("  GET  /v1/functions")
    print("  GET  /docs (API documentation)")
    print("\nServer will be available at: http://localhost:8000")

    uvicorn.run(
        "openai_api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
