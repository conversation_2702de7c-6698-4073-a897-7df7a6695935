"""
OpenAI-compatible API wrapper for QJ Robots Perception services.

This module provides an OpenAI-style interface for accessing perception capabilities,
making it easy to integrate with existing OpenAI-based applications and workflows.
"""

from typing import List, Dict, Union, Optional, Iterator, Any, Literal, Callable
from dataclasses import dataclass, asdict
import json
import time
import uuid
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from py_qj_robots.perception import Perception


@dataclass
class Message:
    """OpenAI-style message structure"""
    role: Literal["system", "user", "assistant"]
    content: str
    name: Optional[str] = None


@dataclass
class Choice:
    """OpenAI-style choice structure"""
    index: int
    message: Message
    finish_reason: Optional[str] = None


@dataclass
class Usage:
    """OpenAI-style usage statistics"""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


@dataclass
class ChatCompletion:
    """OpenAI-style chat completion response"""
    id: str
    object: str = "chat.completion"
    created: Optional[int] = None
    model: str = "qj-perception-v1"
    choices: Optional[List[Choice]] = None
    usage: Optional[Usage] = None

    def __post_init__(self):
        if self.created is None:
            self.created = int(time.time())
        if self.choices is None:
            self.choices = []


@dataclass
class ChatCompletionChunk:
    """OpenAI-style streaming chunk"""
    id: str
    object: str = "chat.completion.chunk"
    created: Optional[int] = None
    model: str = "qj-perception-v1"
    choices: Optional[List[Dict]] = None

    def __post_init__(self):
        if self.created is None:
            self.created = int(time.time())
        if self.choices is None:
            self.choices = []


class PerceptionFunction:
    """Represents a perception function that can be called"""
    
    def __init__(self, name: str, description: str, parameters: Dict):
        self.name = name
        self.description = description
        self.parameters = parameters
    
    def to_dict(self) -> Dict:
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters
            }
        }


class OpenAIPerceptionAPI:
    """OpenAI-compatible API for QJ Robots Perception services"""
    
    def __init__(self, enable_async_mode: bool = False, max_workers: int = 10):
        """Initialize the OpenAI-compatible perception API
        
        Args:
            enable_async_mode (bool): Enable asynchronous processing
            max_workers (int): Maximum number of worker threads for async mode
        """
        self.perception = Perception(enable_async_mode=enable_async_mode, max_workers=max_workers)
        self._setup_functions()
    
    def _setup_functions(self):
        """Setup available perception functions"""
        self.functions = {
            "check_image": PerceptionFunction(
                name="check_image",
                description="Check and detect objects in an image",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {
                            "type": "string",
                            "enum": ["2D", "3D"],
                            "description": "Type of image to process"
                        },
                        "image_url": {
                            "type": "string",
                            "description": "URL of the image to analyze"
                        },
                        "object_names": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Names of objects to detect"
                        },
                        "depth_url": {
                            "type": "string",
                            "description": "URL of depth image (required for 3D)"
                        }
                    },
                    "required": ["image_type", "image_url", "object_names"]
                }
            ),
            "split_image": PerceptionFunction(
                name="split_image",
                description="Segment and split objects in an image",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {"type": "string", "enum": ["2D", "3D"]},
                        "image_url": {"type": "string"},
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "depth_url": {"type": "string"}
                    },
                    "required": ["image_type", "image_url", "object_names"]
                }
            ),
            "props_describe": PerceptionFunction(
                name="props_describe",
                description="Get detailed property descriptions of objects",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {"type": "string", "enum": ["2D", "3D"]},
                        "image_url": {"type": "string"},
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "questions": {"type": "array", "items": {"type": "string"}},
                        "depth_url": {"type": "string"}
                    },
                    "required": ["image_type", "image_url", "object_names", "questions"]
                }
            ),
            "angle_prediction": PerceptionFunction(
                name="angle_prediction",
                description="Predict angles of objects in an image",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {"type": "string", "enum": ["2D", "3D"]},
                        "image_url": {"type": "string"},
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "depth_url": {"type": "string"}
                    },
                    "required": ["image_type", "image_url", "object_names"]
                }
            ),
            "key_point_prediction": PerceptionFunction(
                name="key_point_prediction",
                description="Predict key points of objects in an image",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {"type": "string", "enum": ["2D", "3D"]},
                        "image_url": {"type": "string"},
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "depth_url": {"type": "string"}
                    },
                    "required": ["image_type", "image_url", "object_names"]
                }
            ),
            "grab_point_prediction": PerceptionFunction(
                name="grab_point_prediction",
                description="Predict grab points for robotic manipulation",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {"type": "string", "enum": ["2D", "3D"]},
                        "image_url": {"type": "string"},
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "depth_url": {"type": "string"}
                    },
                    "required": ["image_type", "image_url", "object_names"]
                }
            ),
            "full_perception": PerceptionFunction(
                name="full_perception",
                description="Run comprehensive perception analysis with all functions",
                parameters={
                    "type": "object",
                    "properties": {
                        "image_type": {"type": "string", "enum": ["2D", "3D"]},
                        "image_url": {"type": "string"},
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "questions": {"type": "array", "items": {"type": "string"}},
                        "depth_url": {"type": "string"}
                    },
                    "required": ["image_type", "image_url", "object_names", "questions"]
                }
            )
        }
    
    def list_functions(self) -> List[Dict]:
        """List all available perception functions in OpenAI format"""
        return [func.to_dict() for func in self.functions.values()]

    def _parse_function_call(self, message_content: str) -> Optional[Dict]:
        """Parse function call from message content"""
        try:
            # Look for function call patterns in the message
            if "function_call:" in message_content.lower():
                lines = message_content.split('\n')
                for line in lines:
                    if line.strip().startswith('{') and line.strip().endswith('}'):
                        return json.loads(line.strip())

            # Try to parse the entire content as JSON
            if message_content.strip().startswith('{'):
                return json.loads(message_content.strip())

        except json.JSONDecodeError:
            pass

        return None

    def _execute_function(self, function_name: str, arguments: Dict) -> Dict:
        """Execute a perception function with given arguments"""
        if function_name not in self.functions:
            raise ValueError(f"Unknown function: {function_name}")

        # Map function names to perception methods
        method_map = {
            "check_image": self.perception.check_image,
            "split_image": self.perception.split_image,
            "props_describe": self.perception.props_describe,
            "angle_prediction": self.perception.angle_prediction,
            "key_point_prediction": self.perception.key_point_prediction,
            "grab_point_prediction": self.perception.grab_point_prediction,
            "full_perception": self.perception.full_perception
        }

        method = method_map[function_name]

        try:
            result = method(**arguments)
            return {
                "success": True,
                "function_name": function_name,
                "result": result
            }
        except Exception as e:
            return {
                "success": False,
                "function_name": function_name,
                "error": str(e)
            }

    def _format_perception_result(self, result: Dict) -> str:
        """Format perception result into a readable response"""
        if not result.get("success", False):
            return f"Error executing {result.get('function_name', 'unknown')}: {result.get('error', 'Unknown error')}"

        function_name = result["function_name"]
        data = result["result"]

        # Format based on function type
        if function_name == "check_image":
            return self._format_check_result(data)
        elif function_name == "split_image":
            return self._format_split_result(data)
        elif function_name == "props_describe":
            return self._format_props_result(data)
        elif function_name == "angle_prediction":
            return self._format_angle_result(data)
        elif function_name == "key_point_prediction":
            return self._format_keypoint_result(data)
        elif function_name == "grab_point_prediction":
            return self._format_grasp_result(data)
        elif function_name == "full_perception":
            return self._format_full_result(data)
        else:
            return f"Perception analysis completed. Raw result: {json.dumps(data, indent=2)}"

    def _format_check_result(self, data: Dict) -> str:
        """Format check_image result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])
        scores = task_result.get("scores", [])
        boxes = task_result.get("boxes", [])

        if not labels:
            return "No objects detected in the image."

        result_lines = ["Object Detection Results:"]
        for i, (label, score, box) in enumerate(zip(labels, scores, boxes)):
            result_lines.append(f"- {label}: confidence {score:.2f}, bbox {box}")

        return "\n".join(result_lines)

    def _format_split_result(self, data: Dict) -> str:
        """Format split_image result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])
        masks = task_result.get("masks", [])
        cropped_images = task_result.get("croppedImagesListBbox", [])

        result_lines = ["Image Segmentation Results:"]
        result_lines.append(f"Found {len(labels)} objects with segmentation masks")

        for i, label in enumerate(labels):
            result_lines.append(f"- {label}")
            if i < len(cropped_images):
                result_lines.append(f"  Cropped image: {cropped_images[i]}")
            if i < len(masks):
                mask_info = masks[i]
                if isinstance(mask_info, dict):
                    if "maskImage" in mask_info:
                        result_lines.append(f"  Mask image: {mask_info['maskImage']}")

        return "\n".join(result_lines)

    def _format_props_result(self, data: Dict) -> str:
        """Format props_describe result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])
        answers = task_result.get("answers", [])
        questions = task_result.get("questions", [])

        result_lines = ["Object Property Analysis Results:"]

        for i, label in enumerate(labels):
            result_lines.append(f"\n{label}:")
            if i < len(answers) and isinstance(answers[i], list):
                for j, answer in enumerate(answers[i]):
                    if j < len(questions):
                        result_lines.append(f"  Q: {questions[j]}")
                        result_lines.append(f"  A: {answer}")

        return "\n".join(result_lines)

    def _format_angle_result(self, data: Dict) -> str:
        """Format angle_prediction result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])
        angles = task_result.get("angles", [])
        cropped_images = task_result.get("croppedImagesListAngle", [])

        result_lines = ["Angle Prediction Results:"]

        for i, label in enumerate(labels):
            result_lines.append(f"\n{label}:")
            if i < len(angles):
                angle_info = angles[i]
                if isinstance(angle_info, dict):
                    if "angle" in angle_info:
                        result_lines.append(f"  Angle: {angle_info['angle']}°")
                    if "cornerPoints" in angle_info:
                        result_lines.append(f"  Corner points: {angle_info['cornerPoints']}")
            if i < len(cropped_images):
                result_lines.append(f"  Cropped image: {cropped_images[i]}")

        return "\n".join(result_lines)

    def _format_keypoint_result(self, data: Dict) -> str:
        """Format key_point_prediction result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])
        points = task_result.get("points", [])
        cropped_images = task_result.get("croppedImagesListPoint", [])

        result_lines = ["Key Point Prediction Results:"]

        for i, label in enumerate(labels):
            result_lines.append(f"\n{label}:")
            if i < len(points):
                point_info = points[i]
                if isinstance(point_info, dict):
                    if "pointBoxes" in point_info:
                        result_lines.append(f"  Point boxes: {point_info['pointBoxes']}")
                    if "pointLabels" in point_info:
                        result_lines.append(f"  Point labels: {point_info['pointLabels']}")
            if i < len(cropped_images):
                result_lines.append(f"  Cropped image: {cropped_images[i]}")

        return "\n".join(result_lines)

    def _format_grasp_result(self, data: Dict) -> str:
        """Format grab_point_prediction result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])
        grasps = task_result.get("grasps", [])
        cropped_images = task_result.get("croppedImagesListGrasp", [])

        result_lines = ["Grasp Point Prediction Results:"]

        for i, label in enumerate(labels):
            result_lines.append(f"\n{label}:")
            if i < len(grasps):
                grasp_info = grasps[i]
                if isinstance(grasp_info, dict):
                    if "graspPoint" in grasp_info:
                        result_lines.append(f"  Grasp point: {grasp_info['graspPoint']}")
                    if "graspAngle" in grasp_info:
                        result_lines.append(f"  Grasp angle: {grasp_info['graspAngle']}°")
            if i < len(cropped_images):
                result_lines.append(f"  Cropped image: {cropped_images[i]}")

        return "\n".join(result_lines)

    def _format_full_result(self, data: Dict) -> str:
        """Format full_perception result"""
        task_result = data.get("taskResult", {})
        labels = task_result.get("labels", [])

        result_lines = ["Comprehensive Perception Analysis Results:"]
        result_lines.append(f"Detected {len(labels)} objects: {', '.join(labels)}")

        # Add summary of each analysis type
        if task_result.get("boxes"):
            result_lines.append(f"✓ Object detection: {len(task_result['boxes'])} bounding boxes")
        if task_result.get("masks"):
            result_lines.append(f"✓ Segmentation: {len(task_result['masks'])} masks")
        if task_result.get("angles"):
            result_lines.append(f"✓ Angle analysis: {len(task_result['angles'])} angle measurements")
        if task_result.get("points"):
            result_lines.append(f"✓ Key points: {len(task_result['points'])} point sets")
        if task_result.get("grasps"):
            result_lines.append(f"✓ Grasp points: {len(task_result['grasps'])} grasp candidates")
        if task_result.get("answers"):
            result_lines.append(f"✓ Property descriptions: {len(task_result['answers'])} object descriptions")

        return "\n".join(result_lines)

    def create_chat_completion(
        self,
        messages: List[Dict],
        model: str = "qj-perception-v1",
        functions: Optional[List[Dict]] = None,
        function_call: Optional[Union[str, Dict]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[ChatCompletion, Iterator[ChatCompletionChunk]]:
        """Create a chat completion using perception capabilities

        Args:
            messages: List of message objects
            model: Model name (ignored, always uses perception)
            functions: List of available functions (auto-populated if None)
            function_call: How to handle function calls
            temperature: Sampling temperature (ignored for perception)
            max_tokens: Maximum tokens (ignored for perception)
            stream: Whether to stream the response
            **kwargs: Additional arguments (ignored)

        Returns:
            ChatCompletion or Iterator of ChatCompletionChunk
        """
        if functions is None:
            functions = self.list_functions()

        # Generate unique ID for this completion
        completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"

        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg
                break

        if not user_message:
            raise ValueError("No user message found in conversation")

        # Try to parse function call from user message
        function_call_data = self._parse_function_call(user_message["content"])

        if function_call_data and "function" in function_call_data:
            # Execute the function call
            func_name = function_call_data["function"]
            func_args = function_call_data.get("arguments", {})

            result = self._execute_function(func_name, func_args)
            response_content = self._format_perception_result(result)

            # Calculate token usage (rough estimation)
            prompt_tokens = sum(len(msg.get("content", "").split()) for msg in messages)
            completion_tokens = len(response_content.split())

            if stream:
                return self._create_streaming_response(completion_id, response_content, prompt_tokens, completion_tokens)
            else:
                return self._create_completion_response(completion_id, response_content, prompt_tokens, completion_tokens)
        else:
            # No function call detected, provide helpful response
            response_content = self._create_helpful_response(user_message["content"], functions)

            prompt_tokens = sum(len(msg.get("content", "").split()) for msg in messages)
            completion_tokens = len(response_content.split())

            if stream:
                return self._create_streaming_response(completion_id, response_content, prompt_tokens, completion_tokens)
            else:
                return self._create_completion_response(completion_id, response_content, prompt_tokens, completion_tokens)

    def _create_completion_response(self, completion_id: str, content: str, prompt_tokens: int, completion_tokens: int) -> ChatCompletion:
        """Create a non-streaming completion response"""
        return ChatCompletion(
            id=completion_id,
            choices=[
                Choice(
                    index=0,
                    message=Message(role="assistant", content=content),
                    finish_reason="stop"
                )
            ],
            usage=Usage(
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=prompt_tokens + completion_tokens
            )
        )

    def _create_streaming_response(self, completion_id: str, content: str, prompt_tokens: int, completion_tokens: int) -> Iterator[ChatCompletionChunk]:
        """Create a streaming completion response"""
        words = content.split()

        # First chunk with role
        yield ChatCompletionChunk(
            id=completion_id,
            choices=[{
                "index": 0,
                "delta": {"role": "assistant"},
                "finish_reason": None
            }]
        )

        # Content chunks
        for i, word in enumerate(words):
            chunk_content = word + (" " if i < len(words) - 1 else "")
            yield ChatCompletionChunk(
                id=completion_id,
                choices=[{
                    "index": 0,
                    "delta": {"content": chunk_content},
                    "finish_reason": None
                }]
            )
            time.sleep(0.05)  # Simulate streaming delay

        # Final chunk
        yield ChatCompletionChunk(
            id=completion_id,
            choices=[{
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }]
        )

    def _create_helpful_response(self, user_content: str, functions: List[Dict]) -> str:
        """Create a helpful response when no function call is detected"""
        response_lines = [
            "I'm a QJ Robots Perception API assistant. I can help you analyze images using various perception functions.",
            "",
            "To use my capabilities, please provide a function call in JSON format. Available functions:",
            ""
        ]

        for func in functions:
            func_info = func.get("function", {})
            name = func_info.get("name", "")
            description = func_info.get("description", "")
            response_lines.append(f"• {name}: {description}")

        response_lines.extend([
            "",
            "Example usage:",
            '{"function": "check_image", "arguments": {"image_type": "2D", "image_url": "https://example.com/image.jpg", "object_names": ["apple", "banana"]}}',
            "",
            f"Your message: {user_content[:100]}{'...' if len(user_content) > 100 else ''}"
        ])

        return "\n".join(response_lines)

    # Async versions of the API
    def create_chat_completion_async(
        self,
        messages: List[Dict],
        callback: Optional[Callable] = None,
        timeout: int = 30,
        **kwargs
    ) -> str:
        """Create an async chat completion

        Args:
            messages: List of message objects
            callback: Callback function for when completion is ready
            timeout: Timeout for the operation
            **kwargs: Additional arguments passed to create_chat_completion

        Returns:
            str: Task ID for tracking the async operation
        """
        if not self.perception.enable_async_mode:
            raise RuntimeError("Async mode is not enabled. Initialize with enable_async_mode=True")

        # Get the last user message and parse function call
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg
                break

        if not user_message:
            raise ValueError("No user message found in conversation")

        function_call_data = self._parse_function_call(user_message["content"])

        if function_call_data and "function" in function_call_data:
            func_name = function_call_data["function"]
            func_args = function_call_data.get("arguments", {})

            # Map to async perception methods
            async_method_map = {
                "check_image": self.perception.check_image_async,
                "split_image": self.perception.split_image_async,
                "props_describe": self.perception.props_describe_async,
                "angle_prediction": self.perception.angle_prediction_async,
                "key_point_prediction": self.perception.key_point_prediction_async,
                "grab_point_prediction": self.perception.grab_point_prediction_async,
                "full_perception": self.perception.full_perception_async
            }

            if func_name not in async_method_map:
                raise ValueError(f"Unknown function: {func_name}")

            # Create wrapper callback that formats the result
            def result_callback(result, error):
                if callback:
                    if error:
                        callback(None, error)
                    else:
                        formatted_result = self._format_perception_result({
                            "success": True,
                            "function_name": func_name,
                            "result": result
                        })
                        completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
                        completion = self._create_completion_response(
                            completion_id, formatted_result, 0, len(formatted_result.split())
                        )
                        callback(completion, None)

            method = async_method_map[func_name]
            return method(callback=result_callback, timeout=timeout, **func_args)
        else:
            raise ValueError("No valid function call found in user message")
