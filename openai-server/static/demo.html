<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感知API测试工具 - QJ Robots</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <link href="demo.css" rel="stylesheet">
</head>

<body>
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- 侧边栏 -->
            <div class="col-lg-3 col-md-4 sidebar bg-light border-end d-flex flex-column">
                <!-- 顶部导航栏 -->
                <div class="sidebar-header p-3 border-bottom bg-white">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-primary rounded-circle p-2 me-2">
                                <i class="bi bi-robot text-white"></i>
                            </div>
                            <div>
                                <h5 class="mb-0">感知API</h5>
                                <small class="text-muted">智能测试工具</small>
                            </div>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button"
                                data-bs-toggle="dropdown">
                                <i class="bi bi-gear"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" id="themeToggle"><i class="bi bi-moon"></i>
                                        深色模式</a></li>

                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="#" id="clearHistory"><i class="bi bi-trash"></i>
                                        清除历史</a></li>
                            </ul>
                        </div>
                    </div>
                    <button class="btn btn-primary w-100 mb-3" id="newChatBtn">
                        <i class="bi bi-plus-circle me-1"></i> 新建对话
                    </button>

                    <!-- 配置区域 -->
                    <div class="config-section">
                        <h6 class="text-muted mb-2">
                            <i class="bi bi-sliders me-1"></i>配置
                        </h6>

                        <!-- Base URL -->
                        <div class="mb-2">
                            <label class="form-label small text-muted mb-1">Base URL</label>
                            <input type="text" class="form-control form-control-sm" id="baseUrl"
                                value="https://jwd.vooice.tech/openai" placeholder="API服务地址">
                        </div>

                        <!-- API Key -->
                        <div class="mb-2">
                            <label class="form-label small text-muted mb-1">API Key</label>
                            <div class="input-group input-group-sm">
                                <input type="password" class="form-control" id="apiKey"
                                    value="sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc" placeholder="访问凭证">
                                <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 模型选择 -->
                        <div class="mb-2">
                            <label class="form-label small text-muted mb-1">模型</label>
                            <select class="form-select form-select-sm" id="modelSelect">
                                <option value="qj-perception-v1">qj-perception-v1</option>
                            </select>
                        </div>

                        <!-- 输出方式 -->
                        <div class="mb-2">
                            <label class="form-label small text-muted mb-1">输出方式</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="outputMode" id="blockingMode"
                                    value="blocking" checked>
                                <label class="btn btn-outline-primary btn-sm" for="blockingMode">
                                    <i class="bi bi-pause-circle me-1"></i>阻塞
                                </label>
                                <input type="radio" class="btn-check" name="outputMode" id="streamingMode"
                                    value="streaming">
                                <label class="btn btn-outline-primary btn-sm" for="streamingMode">
                                    <i class="bi bi-play-circle me-1"></i>流式
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 聊天历史 -->
                <div class="chat-history flex-grow-1 overflow-auto p-3">
                    <div class="d-flex align-items-center justify-content-between mb-2">
                        <h6 class="text-muted mb-0">
                            <i class="bi bi-clock-history me-1"></i>聊天历史
                        </h6>
                        <span class="badge bg-secondary" id="chatCount">0</span>
                    </div>
                    <div id="chatList" class="list-group list-group-flush">
                        <!-- 聊天历史项目将在这里动态添加 -->
                    </div>
                </div>


            </div>

            <!-- 主聊天区域 -->
            <div class="col-lg-9 col-md-8 main-chat d-flex flex-column">
                <!-- 顶部工具栏 -->
                <div class="chat-header bg-white border-bottom p-3">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <button class="btn btn-outline-secondary d-md-none me-2" type="button"
                                data-bs-toggle="offcanvas" data-bs-target="#sidebarOffcanvas">
                                <i class="bi bi-list"></i>
                            </button>
                            <div>
                                <h6 class="mb-0" id="currentChatTitle">新对话</h6>
                                <small class="text-muted" id="chatStatus">准备就绪</small>
                            </div>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="exportChat"
                                    title="导出对话">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="shareChat"
                                    title="分享对话">
                                    <i class="bi bi-share"></i>
                                </button>
                            </div>
                            <div class="vr"></div>
                            <span class="badge bg-success" id="connectionStatus">
                                <i class="bi bi-wifi"></i> 已连接
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 聊天消息区域 -->
                <div class="chat-messages flex-grow-1 overflow-auto p-4" id="chatMessages">
                    <div class="welcome-message text-center py-5">
                        <div class="mb-4">
                            <div class="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center"
                                style="width: 80px; height: 80px;">
                                <i class="bi bi-robot text-white" style="font-size: 2rem;"></i>
                            </div>
                        </div>
                        <h3 class="mb-3">欢迎使用感知API测试工具</h3>
                        <p class="text-muted mb-4">上传图片并输入问题，开始测试感知API的强大功能！</p>
                        <div class="row g-3 justify-content-center">
                            <div class="col-md-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-image text-primary mb-2" style="font-size: 1.5rem;"></i>
                                        <h6>图像识别</h6>
                                        <small class="text-muted">上传图片进行智能分析</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-lightning text-warning mb-2" style="font-size: 1.5rem;"></i>
                                        <h6>实时流式</h6>
                                        <small class="text-muted">体验流式输出效果</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 bg-light">
                                    <div class="card-body text-center">
                                        <i class="bi bi-chat-dots text-success mb-2" style="font-size: 1.5rem;"></i>
                                        <h6>智能对话</h6>
                                        <small class="text-muted">多轮对话交互</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="input-area border-top bg-white p-3">
                    <!-- 已上传图片预览区域 -->
                    <div class="uploaded-image-preview mb-3" id="uploadedImagePreview" style="display: none;">
                        <div class="position-relative d-inline-block">
                            <img id="previewImage" src="" alt="预览图片" class="rounded-3 border"
                                style="max-width: 200px; max-height: 150px;">
                            <button class="btn btn-sm btn-danger position-absolute top-0 end-0 rounded-circle"
                                id="removeImageBtn" style="transform: translate(50%, -50%);">
                                <i class="bi bi-x"></i>
                            </button>
                            <div class="upload-status position-absolute top-50 start-50 translate-middle bg-dark bg-opacity-75 text-white rounded px-3 py-2"
                                id="uploadStatus" style="display: none;">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                上传中...
                            </div>
                        </div>
                    </div>

                    <!-- 隐藏的文件输入 -->
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">

                    <!-- 文本输入区域 -->
                    <div class="text-input-area">
                        <div class="input-group">
                            <textarea class="form-control border-0 shadow-sm" id="messageInput" placeholder="请输入您的问题..."
                                rows="3" style="resize: none;"></textarea>
                            <div class="input-group-append d-flex flex-column justify-content-end">
                                <button class="btn btn-primary rounded-circle ms-2 mb-1" id="sendBtn" disabled
                                    style="width: 40px; height: 40px;">
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div class="d-flex gap-2">
                                <!-- 图片上传按钮 - 放在最左侧 -->
                                <button class="btn btn-outline-secondary" id="imageUploadBtn" title="上传图片"
                                    style="width: 40px; height: 40px;">
                                    <i class="bi bi-image" style="font-size: 1.2rem;"></i>
                                </button>

                                <button class="btn btn-outline-secondary" id="attachFileBtn" title="上传文件"
                                    style="width: 40px; height: 40px;">
                                    <i class="bi bi-paperclip" style="font-size: 1.2rem;"></i>
                                </button>
                                <button class="btn btn-outline-secondary" id="voiceInputBtn" title="语音输入"
                                    style="width: 40px; height: 40px;">
                                    <i class="bi bi-mic" style="font-size: 1.2rem;"></i>
                                </button>
                                <button class="btn btn-outline-secondary" id="emojiBtn" title="表情"
                                    style="width: 40px; height: 40px;">
                                    <i class="bi bi-emoji-smile" style="font-size: 1.2rem;"></i>
                                </button>
                            </div>
                            <small class="text-muted">
                                <span id="charCount">0</span>/2000 字符
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 加载中模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">处理中...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer">
        <!-- Toast 消息将在这里动态添加 -->
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q"
        crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.10/marked.min.js"></script>
    <script src="demo.js"></script>
</body>

</html>