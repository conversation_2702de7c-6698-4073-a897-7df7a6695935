Metadata-Version: 2.4
Name: py-qj-robots
Version: 0.1.13
Summary: QJ Python SDK
Home-page: https://github.com/QJ-ROBOTS/perception-python-sdk
Author: QJ ROBOTS
Author-email: <EMAIL>
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.0
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: requests>=2.26.0
Requires-Dist: python-dotenv>=0.19.0
Provides-Extra: openai
Requires-Dist: fastapi>=0.68.0; extra == "openai"
Requires-Dist: uvicorn[standard]>=0.15.0; extra == "openai"
Requires-Dist: pydantic>=1.8.0; extra == "openai"
Provides-Extra: dev
Requires-Dist: fastapi>=0.68.0; extra == "dev"
Requires-Dist: uvicorn[standard]>=0.15.0; extra == "dev"
Requires-Dist: pydantic>=1.8.0; extra == "dev"
Requires-Dist: pytest>=6.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.18.0; extra == "dev"
Requires-Dist: httpx>=0.23.0; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license-file
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

QJ Python SDK for perception capabilities
