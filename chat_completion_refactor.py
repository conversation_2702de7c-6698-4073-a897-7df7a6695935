#!/usr/bin/env python3
"""
重构后的聊天完成实现 - 抽象流式和非流式输出
"""

import json
import time
import uuid
from typing import Dict, List, Any, Optional, Iterator, Union
from fastapi.responses import StreamingResponse


class ChatCompletionHandler:
    """聊天完成处理器 - 抽象流式和非流式输出"""
    
    def __init__(self, perception_api, logger):
        self.perception_api = perception_api
        self.logger = logger
    
    def create_completion_id(self) -> str:
        """生成唯一的完成ID"""
        return f"chatcmpl-{uuid.uuid4().hex[:29]}"
    
    def create_base_response(self, completion_id: str, model: str, is_chunk: bool = False) -> Dict:
        """创建基础响应结构"""
        return {
            "id": completion_id,
            "object": "chat.completion.chunk" if is_chunk else "chat.completion",
            "created": int(time.time()),
            "model": model
        }
    
    def create_choice(self, content: str = "", role: str = "assistant", 
                     finish_reason: Optional[str] = None, is_delta: bool = False) -> Dict:
        """创建选择项"""
        if is_delta:
            choice = {
                "index": 0,
                "delta": {},
                "finish_reason": finish_reason
            }
            if content:
                choice["delta"]["content"] = content
            if role and not finish_reason:  # 只在开始时设置role
                choice["delta"]["role"] = role
        else:
            choice = {
                "index": 0,
                "message": {
                    "role": role,
                    "content": content
                },
                "finish_reason": finish_reason or "stop"
            }
        return choice
    
    def process_perception_request(self, image_url: str, text_content: str, 
                                 function_name: str = "check_image", 
                                 **kwargs) -> str:
        """处理感知请求的核心逻辑"""
        from py_qj_robots.perception import Perception
        
        # 创建感知API实例
        perception = Perception()
        
        # 从用户文字中提取对象名称
        object_names = self.extract_object_names_from_text(text_content)
        if not object_names:
            object_names = ["car", "person", "object"]  # 默认对象
        
        # 根据function_name调用不同的感知API
        if function_name == "check_image":
            result = perception.check_image(
                image_type="2D",
                image_url=image_url,
                object_names=object_names
            )
        elif function_name == "split_image":
            result = perception.split_image(
                image_type="2D",
                image_url=image_url,
                object_names=object_names
            )
        elif function_name == "props_describe":
            questions = kwargs.get('questions', ["What is this object?"])
            result = perception.props_describe(
                image_type="2D",
                image_url=image_url,
                object_names=object_names,
                questions=questions
            )
        elif function_name == "angle_prediction":
            result = perception.angle_prediction(
                image_type="2D",
                image_url=image_url,
                object_names=object_names
            )
        elif function_name == "key_point_prediction":
            result = perception.key_point_prediction(
                image_type="2D",
                image_url=image_url,
                object_names=object_names
            )
        elif function_name == "grab_point_prediction":
            result = perception.grab_point_prediction(
                image_type="2D",
                image_url=image_url,
                object_names=object_names
            )
        elif function_name == "full_perception":
            questions = kwargs.get('questions', ["Describe this image in detail"])
            result = perception.full_perception(
                image_type="2D",
                image_url=image_url,
                object_names=object_names,
                questions=questions
            )
        else:
            raise ValueError(f"Unknown function: {function_name}")
        
        # 解析和格式化结果
        content_parts = self.parse_perception_result(result, text_content)
        return "".join(content_parts)
    
    def create_streaming_response(self, request, image_url: str, text_content: str, 
                                function_name: str = "check_image", **kwargs) -> StreamingResponse:
        """创建流式响应"""
        def generate_stream():
            try:
                completion_id = self.create_completion_id()
                
                # 发送开始chunk
                start_response = self.create_base_response(completion_id, request.model, is_chunk=True)
                start_response["choices"] = [self.create_choice(role="assistant", is_delta=True)]
                yield f"data: {json.dumps(start_response, ensure_ascii=False)}\n\n"
                
                # 发送处理状态信息
                status_messages = [
                    "🔍 正在分析图片...",
                    f"\n📊 调用{function_name}功能...",
                    "\n🤖 处理感知结果..."
                ]
                
                for status_msg in status_messages:
                    status_response = self.create_base_response(completion_id, request.model, is_chunk=True)
                    status_response["choices"] = [self.create_choice(content=status_msg, is_delta=True)]
                    yield f"data: {json.dumps(status_response, ensure_ascii=False)}\n\n"
                    time.sleep(0.5)
                
                # 处理感知请求
                response_content = self.process_perception_request(
                    image_url, text_content, function_name, **kwargs
                )
                
                # 流式发送内容
                words = response_content.split()
                chunk_size = 2
                
                for i in range(0, len(words), chunk_size):
                    chunk_words = words[i:i + chunk_size]
                    chunk_content = " ".join(chunk_words)
                    if i == 0:
                        chunk_content = "\n" + chunk_content
                    else:
                        chunk_content = " " + chunk_content
                    
                    content_response = self.create_base_response(completion_id, request.model, is_chunk=True)
                    content_response["choices"] = [self.create_choice(content=chunk_content, is_delta=True)]
                    yield f"data: {json.dumps(content_response, ensure_ascii=False)}\n\n"
                    time.sleep(0.15)
                
                # 发送结束chunk
                end_response = self.create_base_response(completion_id, request.model, is_chunk=True)
                end_response["choices"] = [self.create_choice(finish_reason="stop", is_delta=True)]
                yield f"data: {json.dumps(end_response, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                self.logger.error(f"Streaming error: {e}")
                error_response = self.create_base_response(
                    f"chatcmpl-{uuid.uuid4().hex[:29]}", request.model, is_chunk=True
                )
                error_response["choices"] = [
                    self.create_choice(content=f"\n\n❌ 处理错误: {str(e)}", 
                                     finish_reason="stop", is_delta=True)
                ]
                yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
    
    def create_non_streaming_response(self, request, image_url: str, text_content: str,
                                    function_name: str = "check_image", **kwargs) -> Dict:
        """创建非流式响应"""
        try:
            completion_id = self.create_completion_id()
            
            # 处理感知请求
            response_content = self.process_perception_request(
                image_url, text_content, function_name, **kwargs
            )
            
            # 创建响应
            response = self.create_base_response(completion_id, request.model)
            response["choices"] = [self.create_choice(content=response_content)]
            response["usage"] = {
                "prompt_tokens": len(text_content.split()),
                "completion_tokens": len(response_content.split()),
                "total_tokens": len(text_content.split()) + len(response_content.split())
            }
            
            return response
            
        except Exception as e:
            self.logger.error(f"Non-streaming error: {e}")
            raise
    
    def determine_function_from_request(self, request, text_content: str) -> tuple:
        """从请求中确定要使用的函数"""
        # 如果请求中指定了functions和function_call，使用指定的函数
        if request.functions and request.function_call:
            if isinstance(request.function_call, dict):
                function_name = request.function_call.get("name", "check_image")
            else:
                function_name = request.function_call
            
            # 从functions中找到对应的函数定义
            for func in request.functions:
                if func.get("name") == function_name:
                    return function_name, func.get("parameters", {})
        
        # 否则根据文本内容智能选择函数
        text_lower = text_content.lower()
        
        if any(word in text_lower for word in ["split", "segment", "separate"]):
            return "split_image", {}
        elif any(word in text_lower for word in ["describe", "property", "detail"]):
            return "props_describe", {"questions": ["Describe this object in detail"]}
        elif any(word in text_lower for word in ["angle", "rotation", "orientation"]):
            return "angle_prediction", {}
        elif any(word in text_lower for word in ["keypoint", "key point", "landmark"]):
            return "key_point_prediction", {}
        elif any(word in text_lower for word in ["grab", "grasp", "pick"]):
            return "grab_point_prediction", {}
        elif any(word in text_lower for word in ["full", "complete", "comprehensive"]):
            return "full_perception", {"questions": ["Provide comprehensive analysis"]}
        else:
            return "check_image", {}
    
    def extract_object_names_from_text(self, text: str) -> List[str]:
        """从文本中提取对象名称"""
        # 这里可以实现更复杂的NLP逻辑
        # 暂时使用简单的关键词匹配
        common_objects = ["car", "person", "object", "vehicle", "human", "building", "tree"]
        text_lower = text.lower()
        found_objects = [obj for obj in common_objects if obj in text_lower]
        return found_objects if found_objects else ["object"]
    
    def parse_perception_result(self, result: Any, text_content: str) -> List[str]:
        """解析感知结果"""
        # 这里应该根据实际的感知API返回格式来解析
        # 暂时返回简单的格式化结果
        if isinstance(result, dict):
            return [f"感知分析结果：{json.dumps(result, ensure_ascii=False, indent=2)}"]
        else:
            return [f"感知分析结果：{str(result)}"]


# 使用示例：重构后的create_chat_completion函数
async def create_chat_completion_refactored(
    request,
    credentials,
    handler: ChatCompletionHandler
):
    """重构后的聊天完成函数"""
    try:
        # 验证token和提取图片文字的逻辑保持不变
        # ... (token验证代码)
        
        # 提取图片和文字
        image_data, text_content = extract_image_and_text(messages)
        image_url = process_image_data(image_data)
        
        # 确定要使用的函数
        function_name, function_params = handler.determine_function_from_request(request, text_content)
        
        # 根据stream参数选择响应类型
        if request.stream:
            return handler.create_streaming_response(
                request, image_url, text_content, function_name, **function_params
            )
        else:
            return handler.create_non_streaming_response(
                request, image_url, text_content, function_name, **function_params
            )
            
    except Exception as e:
        # 错误处理逻辑
        raise HTTPException(status_code=500, detail=str(e))
