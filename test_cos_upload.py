#!/usr/bin/env python3
"""
腾讯云COS上传测试脚本
用于测试图片上传流程的正确性
"""

import requests
import json
from pathlib import Path

def test_upload_params():
    """测试获取上传参数接口"""
    print("🧪 测试获取上传参数接口...")
    
    # 根据环境选择接口地址
    upload_params_url = "http://localhost:9999/open-apis/base/upload/params"
    
    try:
        response = requests.get(upload_params_url)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('code') == 0:
                upload_url = data['data']['url']
                key = data['data']['key']
                print(f"   ✅ 获取上传参数成功")
                print(f"   上传URL: {upload_url}")
                print(f"   文件Key: {key}")
                return upload_url, key
            else:
                print(f"   ❌ 业务错误: {data.get('message')}")
                return None, None
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None, None

def test_cos_upload(upload_url, test_image_path):
    """测试上传文件到腾讯云COS"""
    print(f"🧪 测试上传文件到腾讯云COS...")
    print(f"   测试文件: {test_image_path}")
    print(f"   上传URL: {upload_url}")
    
    if not Path(test_image_path).exists():
        print(f"   ❌ 测试文件不存在: {test_image_path}")
        return False
    
    try:
        # 读取文件内容
        with open(test_image_path, 'rb') as f:
            file_content = f.read()
        
        # 确定Content-Type
        if test_image_path.lower().endswith('.png'):
            content_type = 'image/png'
        elif test_image_path.lower().endswith(('.jpg', '.jpeg')):
            content_type = 'image/jpeg'
        elif test_image_path.lower().endswith('.gif'):
            content_type = 'image/gif'
        else:
            content_type = 'application/octet-stream'
        
        print(f"   Content-Type: {content_type}")
        print(f"   文件大小: {len(file_content)} bytes")
        
        # 发送PUT请求上传文件
        response = requests.put(
            upload_url,
            headers={
                'Content-Type': content_type
            },
            data=file_content
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print(f"   ✅ 文件上传成功")
            return True
        else:
            print(f"   ❌ 文件上传失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 上传异常: {e}")
        return False

def test_final_url(key):
    """测试最终的图片URL是否可访问"""
    print(f"🧪 测试最终图片URL...")
    
    final_url = f"https://cos-cdn-v1.qj-robots.com/{key}"
    print(f"   最终URL: {final_url}")
    
    try:
        response = requests.head(final_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ 图片URL可访问")
            print(f"   Content-Type: {response.headers.get('Content-Type')}")
            print(f"   Content-Length: {response.headers.get('Content-Length')}")
            return True
        else:
            print(f"   ❌ 图片URL不可访问: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 访问异常: {e}")
        return False

def create_test_image():
    """创建一个简单的测试图片"""
    test_image_path = "test_upload_image.png"
    
    try:
        from PIL import Image, ImageDraw
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (100, 100), color='red')
        draw = ImageDraw.Draw(img)
        draw.text((10, 40), "TEST", fill='white')
        img.save(test_image_path)
        
        print(f"   ✅ 创建测试图片: {test_image_path}")
        return test_image_path
        
    except ImportError:
        print("   ⚠️ PIL库未安装，无法创建测试图片")
        print("   请手动提供测试图片路径")
        return None

def main():
    """主测试流程"""
    print("🚀 开始腾讯云COS上传流程测试")
    print("=" * 50)
    
    # 1. 测试获取上传参数
    upload_url, key = test_upload_params()
    if not upload_url or not key:
        print("❌ 获取上传参数失败，测试终止")
        return
    
    print()
    
    # 2. 准备测试图片
    test_image_path = "test_images/red_car.jpg"
    if not Path(test_image_path).exists():
        print("📸 寻找测试图片...")
        # 尝试其他测试图片
        for img_name in ["blue_car.jpg", "cars_street.jpg", "parking_lot.jpg", "traffic.jpg"]:
            alt_path = f"test_images/{img_name}"
            if Path(alt_path).exists():
                test_image_path = alt_path
                break
        else:
            print("❌ 找不到测试图片，请确保test_images目录中有图片文件")
            return
    
    print()
    
    # 3. 测试上传文件
    upload_success = test_cos_upload(upload_url, test_image_path)
    if not upload_success:
        print("❌ 文件上传失败，测试终止")
        return
    
    print()
    
    # 4. 测试最终URL
    url_success = test_final_url(key)
    
    print()
    print("=" * 50)
    
    if upload_success and url_success:
        print("🎉 腾讯云COS上传流程测试成功！")
        print(f"   最终图片URL: https://cos-cdn-v1.qj-robots.com/{key}")
    else:
        print("❌ 腾讯云COS上传流程测试失败")
    
    # 清理测试文件
    if Path(test_image_path).exists() and test_image_path == "test_upload_image.png":
        Path(test_image_path).unlink()
        print(f"   🧹 清理测试文件: {test_image_path}")

if __name__ == "__main__":
    main()
