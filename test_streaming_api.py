#!/usr/bin/env python3
"""
测试流式 API 响应

专门用于测试 OpenAI 兼容的流式 API 功能
"""

import requests
import json
import sys

# API 配置
BASE_URL = "http://localhost:8000"  # 直接测试本地服务器
API_KEY = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"

def test_streaming_api(image_url, question):
    """测试流式 API"""
    print(f"🌊 测试流式 API")
    print(f"📍 API 端点: {BASE_URL}")
    print(f"🖼️  图片URL: {image_url}")
    print(f"❓ 问题: {question}")
    print("-" * 60)
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "qj-perception-v1",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": question
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }
        ],
        "stream": True,
        "max_tokens": 500
    }
    
    try:
        print("🚀 开始流式请求...")
        response = requests.post(
            f"{BASE_URL}/v1/chat/completions",
            headers=headers,
            json=data,
            stream=True,
            timeout=120  # 2分钟超时
        )
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        print("🤖 AI 流式响应:")
        print("-" * 40)
        
        # 处理流式响应
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]  # 移除 'data: ' 前缀
                    
                    if data_str.strip() == '[DONE]':
                        print("\n" + "-" * 40)
                        print("✅ 流式响应完成")
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        choices = chunk_data.get('choices', [])
                        if choices:
                            delta = choices[0].get('delta', {})
                            content = delta.get('content', '')
                            if content:
                                print(content, end='', flush=True)
                    except json.JSONDecodeError:
                        # 忽略无法解析的行
                        pass
        
        return True
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求错误: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python test_streaming_api.py <image_url> [question]")
        print("示例:")
        print("  python test_streaming_api.py 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&q=80'")
        print("  python test_streaming_api.py 'https://example.com/car.jpg' '图中有几辆车？'")
        sys.exit(1)
    
    image_url = sys.argv[1]
    question = sys.argv[2] if len(sys.argv) > 2 else "请分析这张图片，告诉我图中有几辆车，分别是什么颜色？"
    
    print("🎯 OpenAI 流式 API 测试工具")
    print("=" * 60)
    
    success = test_streaming_api(image_url, question)
    
    if success:
        print("\n🎉 流式 API 测试成功！")
    else:
        print("\n⚠️  流式 API 测试失败")

if __name__ == "__main__":
    main()
