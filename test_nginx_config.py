#!/usr/bin/env python3
"""
测试 nginx 配置和 OpenAI API 转发

验证 jwd.vooice.tech/v1 路径是否正确转发到本地 OpenAI API 服务器
"""

import requests
import json
import time
import subprocess
import sys

def check_nginx_config():
    """检查 nginx 配置文件语法"""
    print("🔧 检查 nginx 配置文件语法...")
    try:
        result = subprocess.run(
            ["nginx", "-t", "-c", "conf/nginx.conf"],
            capture_output=True,
            text=True,
            cwd="."
        )
        if result.returncode == 0:
            print("✅ nginx 配置文件语法正确")
            return True
        else:
            print(f"❌ nginx 配置文件语法错误:")
            print(result.stderr)
            return False
    except FileNotFoundError:
        print("⚠️  nginx 命令未找到，跳过语法检查")
        return True
    except Exception as e:
        print(f"❌ 检查配置时出错: {e}")
        return False

def test_local_server():
    """测试本地 OpenAI API 服务器"""
    print("\n🧪 测试本地 OpenAI API 服务器 (端口 8000)...")
    
    endpoints = [
        ("健康检查", "http://localhost:8000/health"),
        ("模型列表", "http://localhost:8000/v1/models"),
        ("根路径", "http://localhost:8000/")
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}: {url} - 正常")
            else:
                print(f"⚠️  {name}: {url} - 状态码 {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: {url} - 连接失败 (服务器可能未启动)")
        except Exception as e:
            print(f"❌ {name}: {url} - 错误: {e}")

def test_domain_forwarding():
    """测试域名转发"""
    print("\n🌐 测试域名转发 (jwd.vooice.tech/v1)...")
    
    endpoints = [
        ("健康检查", "https://jwd.vooice.tech/health"),
        ("模型列表", "https://jwd.vooice.tech/v1/models"),
        ("根路径", "https://jwd.vooice.tech/")
    ]
    
    for name, url in endpoints:
        try:
            response = requests.get(url, timeout=10, verify=False)  # 忽略 SSL 证书验证
            if response.status_code == 200:
                print(f"✅ {name}: {url} - 正常")
                if "models" in url:
                    try:
                        data = response.json()
                        print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                    except:
                        print(f"   响应内容: {response.text[:100]}...")
            else:
                print(f"⚠️  {name}: {url} - 状态码 {response.status_code}")
                print(f"   响应内容: {response.text[:200]}...")
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: {url} - 连接失败")
        except requests.exceptions.Timeout:
            print(f"❌ {name}: {url} - 请求超时")
        except Exception as e:
            print(f"❌ {name}: {url} - 错误: {e}")

def test_openai_api():
    """测试 OpenAI API 兼容性"""
    print("\n🤖 测试 OpenAI API 兼容性...")
    
    # 使用我们的测试 token
    api_key = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试聊天完成 API
    data = {
        "model": "qj-perception-v1",
        "messages": [
            {
                "role": "user",
                "content": json.dumps({
                    "function": "check_image",
                    "arguments": {
                        "image_type": "2D",
                        "image_url": "https://example.com/test.jpg",
                        "object_names": ["car", "person"]
                    }
                })
            }
        ],
        "max_tokens": 100
    }
    
    try:
        response = requests.post(
            "https://jwd.vooice.tech/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30,
            verify=False
        )
        
        if response.status_code == 200:
            print("✅ OpenAI API 调用成功")
            try:
                result = response.json()
                print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)[:300]}...")
            except:
                print(f"   响应内容: {response.text[:200]}...")
        else:
            print(f"⚠️  OpenAI API 调用失败 - 状态码 {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ OpenAI API 调用错误: {e}")

def main():
    """主函数"""
    print("🎯 Nginx 配置和 OpenAI API 转发测试")
    print("=" * 50)
    
    # 检查 nginx 配置
    config_ok = check_nginx_config()
    
    # 测试本地服务器
    test_local_server()
    
    # 测试域名转发
    test_domain_forwarding()
    
    # 测试 OpenAI API
    test_openai_api()
    
    print("\n📋 测试总结:")
    print("1. 确保 OpenAI API 服务器在端口 8000 运行:")
    print("   python openai-server/openai_api_server.py")
    print("   或者: ./bin/start_server.sh")
    print("")
    print("2. 确保 nginx 使用正确的配置文件:")
    print("   nginx -s reload -c $(pwd)/conf/nginx.conf")
    print("   或者: ./bin/restart_nginx.sh")
    print("")
    print("3. 测试 API 端点:")
    print("   curl -k https://jwd.vooice.tech/v1/models")
    print("   curl -k https://jwd.vooice.tech/health")

if __name__ == "__main__":
    main()
