# QJ Robots Python SDK with OpenAI-Compatible API

[![Python Version](https://img.shields.io/badge/python-3.7+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

A comprehensive Python SDK for QJ Robots Perception services with full OpenAI API compatibility.

## 🚀 Features

- **Core Perception SDK**: Complete access to QJ Robots perception capabilities
- **OpenAI-Compatible API**: Use standard OpenAI client libraries and patterns
- **Token Authentication**: Secure Bearer token authentication system
- **REST API Server**: FastAPI-based server with OpenAI-compatible endpoints
- **Async Support**: High-performance asynchronous processing
- **Streaming Responses**: Real-time streaming like OpenAI's API

## 📦 Installation

### Quick Install (Recommended)
```bash
./bin/install_deps.sh
```

### Manual Installation
```bash
pip install -r conf/requirements-openai.txt
```

### Using Installation Script
```bash
python utils/install_dependencies.py
```

## 🎯 Quick Start

### 1. Basic Perception SDK
```python
from py_qj_robots import Perception

perception = Perception()
result = perception.check_image("2D", "image.jpg", ["apple", "banana"])
```

### 2. OpenAI-Compatible API
```python
from py_qj_robots import OpenAIPerceptionAPI
import json

api = OpenAIPerceptionAPI()
response = api.create_chat_completion(messages=[{
    "role": "user",
    "content": json.dumps({
        "function": "check_image",
        "arguments": {
            "image_type": "2D",
            "image_url": "https://example.com/image.jpg",
            "object_names": ["apple", "banana"]
        }
    })
}])
```

### 3. REST API Server
```bash
./bin/start_server.sh
```

Or manually:
```bash
python openai-server/openai_api_server.py
```

Then use with any OpenAI client:
```python
import openai
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "sk-your-token-here"
```

## 📁 Project Structure

```
py-qj-robots/
├── py_qj_robots/           # Core SDK package
│   ├── __init__.py
│   ├── perception.py       # Main perception API
│   ├── authorization.py    # Authentication
│   └── openai_api.py      # OpenAI compatibility layer
├── openai-server/          # OpenAI-compatible API
│   ├── __init__.py
│   ├── openai_api.py      # OpenAI API implementation
│   └── openai_api_server.py # FastAPI server
├── docs/                   # Documentation
│   ├── OPENAI_API_README.md
│   ├── TOKEN_AUTHENTICATION_GUIDE.md
│   └── ...
├── tests/                  # Test scripts
│   ├── test_openai_api.py
│   ├── test_token_auth.py
│   └── ...
├── examples/               # Example scripts
│   ├── openai_api_example.py
│   └── ...
├── utils/                  # Utility scripts
│   ├── token_config.py     # Token management
│   └── ...
├── conf/                   # Configuration files
│   ├── requirements.txt    # Basic dependencies
│   ├── requirements-openai.txt # Full dependencies
│   ├── setup.py           # Package setup
│   └── token_mapping.json # Token configuration
├── output/                 # Test outputs and logs
│   ├── *.csv              # Test results
│   ├── *.txt              # Log files
│   └── api_logs_debug/    # Debug logs
└── bin/                    # Executable scripts
    ├── start_server.sh     # Start API server
    ├── run_tests.sh       # Run test suite
    ├── install_deps.sh    # Install dependencies
    └── manage_tokens.sh   # Manage tokens
```

## 🔧 Configuration

### 1. Set up credentials
```bash
export QJ_APP_ID="your_app_id"
export QJ_APP_SECRET="your_app_secret"
```

### 2. Configure tokens (for OpenAI API)
```bash
./bin/manage_tokens.sh add YOUR_APP_ID YOUR_APP_SECRET --description "Production token"
```

### 3. Start the server
```bash
./bin/start_server.sh
```

## 📚 Documentation

- **[OpenAI API Guide](docs/OPENAI_API_README.md)** - Complete OpenAI compatibility guide
- **[Token Authentication](docs/TOKEN_AUTHENTICATION_GUIDE.md)** - Token setup and management
- **[Installation Guide](docs/INSTALLATION_GUIDE.md)** - Detailed installation instructions
- **[Quick Start Guide](docs/QUICK_START_GUIDE.md)** - Get started quickly

## 🧪 Testing

Run the test suite:
```bash
# Run all tests
./bin/run_tests.sh
```

Or run individual tests:
```bash
# Test OpenAI API compatibility
python tests/test_openai_api.py

# Test token authentication
python tests/test_token_auth.py

# Verify installation
python tests/verify_installation.py
```

## 💡 Examples

Check the `examples/` directory for:
- Direct API usage examples
- OpenAI client examples
- Async usage patterns
- Server integration examples

## 🛠️ Available Perception Functions

1. **check_image** - Object detection and recognition
2. **split_image** - Image segmentation
3. **props_describe** - Object property analysis
4. **angle_prediction** - Angle detection
5. **key_point_prediction** - Key point detection
6. **grab_point_prediction** - Grasp point prediction
7. **full_perception** - Comprehensive analysis

## 🔗 API Endpoints

When running the server (`./bin/start_server.sh`):

- `POST /v1/chat/completions` - OpenAI-compatible chat completions
- `GET /v1/models` - List available models
- `GET /v1/functions` - List perception functions
- `GET /docs` - Interactive API documentation
- `GET /admin/tokens` - Token management (admin)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `python tests/test_openai_api.py`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `docs/` directory
- **Issues**: Report bugs and request features
- **Examples**: See `examples/` for usage patterns

## 🎉 What's New

- ✅ Full OpenAI API compatibility
- ✅ Token-based authentication
- ✅ FastAPI server with auto-documentation
- ✅ Streaming response support
- ✅ Comprehensive test suite
- ✅ CLI management tools

---

**Ready to get started?** Run `./bin/install_deps.sh` and follow the setup guide!
