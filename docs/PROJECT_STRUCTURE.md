# QJ Robots 感知API 项目结构说明

## 📁 优化后的项目目录结构

经过重构优化，项目现在采用清晰的模块化结构，所有文档集中在docs目录，所有测试集中在tests目录。

```
py-qj-robots/
├── 📂 openai-server/                   # 🔧 核心代码模块
│   ├── openai_api_server.py            # 原始服务器文件（备份）
│   ├── openai_api_server_refactored.py # 重构后的主服务器
│   ├── perception_handler.py           # 感知处理器
│   ├── response_generator.py           # 响应生成器
│   ├── function_call_manager.py        # 函数调用管理器
│   ├── performance_optimizer.py        # 性能优化组件
│   └── utils/                          # 工具模块
│       └── token_config.py             # Token配置管理
├── 📂 docs/                            # 📚 完整文档体系（所有MD文件）
│   ├── PROJECT_STRUCTURE.md            # 项目结构说明（本文件）
│   ├── FINAL_REFACTORING_REPORT.md     # 最终重构报告
│   ├── stage1_refactoring_guide.md     # 阶段1重构指南
│   ├── stage1_completion_summary.md    # 阶段1完成总结
│   ├── stage2_function_calling_plan.md # 阶段2实施计划
│   ├── stage2_completion_summary.md    # 阶段2完成总结
│   ├── stage3_testing_optimization_plan.md # 阶段3优化计划
│   ├── stage3_completion_summary.md    # 阶段3完成总结
│   ├── function_calling_examples.md    # Function Calling使用示例
│   └── README.md                       # 项目说明文档
├── 📂 tests/                           # 🧪 测试套件（所有测试脚本）
│   ├── run_all_tests.py                # 统一测试运行脚本 ⭐ 新增
│   ├── test_stage1_refactoring.py      # 阶段1单元测试
│   ├── test_stage2_function_calling.py # 阶段2功能测试
│   ├── test_integration_simple.py      # 简化集成测试
│   └── test_integration_e2e.py         # 端到端集成测试
├── 📂 scripts/                         # 🛠️ 部署和运维脚本
│   └── deploy.sh                       # 自动化部署脚本
├── 📂 py_qj_robots/                    # 📦 核心Python包
│   ├── __init__.py
│   ├── openai_api.py                   # OpenAI API封装
│   └── perception.py                   # 感知API核心
└── 📂 front-end/                       # 🌐 前端Demo（可选）
    └── ...
```

```
py-qj-robots/
├── py_qj_robots/                    # 🏗️ Core SDK Package
│   ├── __init__.py                  # Package initialization
│   ├── perception.py                # Main perception API implementation
│   ├── authorization.py             # Authentication and token management
│   └── openai_api.py               # OpenAI compatibility layer
│
├── openai-server/                   # 🤖 OpenAI-Compatible API
│   ├── __init__.py                  # OpenAI package initialization
│   ├── openai_api.py               # OpenAI API implementation (copy)
│   └── openai_api_server.py        # FastAPI server
│
├── docs/                            # 📚 Documentation
│   ├── OPENAI_API_README.md         # OpenAI API comprehensive guide
│   ├── TOKEN_AUTHENTICATION_GUIDE.md # Token setup and management
│   ├── INSTALLATION_GUIDE.md        # Detailed installation instructions
│   ├── QUICK_START_GUIDE.md         # Quick start tutorial
│   ├── DEPENDENCIES_SUMMARY.md      # Dependencies overview
│   └── PROJECT_STRUCTURE.md         # This file
│
├── tests/                           # 🧪 Test Scripts
│   ├── __init__.py                  # Test package initialization
│   ├── test_openai_api.py          # OpenAI API compatibility tests
│   ├── test_token_auth.py          # Token authentication tests
│   ├── verify_installation.py      # Installation verification
│   ├── async_submit_performance_test.py # Performance tests
│   ├── concurrent_test.py          # Concurrency tests
│   ├── high_performance_test.py    # High performance tests
│   ├── performance_debug_test.py   # Performance debugging
│   ├── qps_validation_test.py      # QPS validation
│   ├── server_concurrency_test.py  # Server concurrency tests
│   └── stress_test.py              # Stress testing
│
├── examples/                        # 💡 Example Scripts
│   ├── __init__.py                  # Examples package initialization
│   ├── openai_api_example.py       # Direct OpenAI API usage examples
│   ├── openai_client_example.py    # HTTP client examples
│   └── async_usage_example.py      # Async usage patterns
│
├── utils/                           # 🛠️ Utility Scripts
│   ├── __init__.py                  # Utils package initialization
│   ├── token_config.py             # Token configuration and management
│   ├── manage_tokens.py            # CLI token management tool
│   └── install_dependencies.py     # Dependency installation script
│
├── conf/                            # ⚙️ Configuration Files
│   ├── __init__.py                  # Config package initialization
│   ├── requirements.txt             # Basic dependencies
│   ├── requirements-openai.txt      # Full OpenAI API dependencies
│   ├── setup.py                    # Package setup configuration
│   └── token_mapping.json          # Token-to-credentials mapping
│
├── output/                          # 📊 Output Files
│   ├── __init__.py                  # Output package initialization
│   ├── *.csv                       # Test result files
│   ├── *.txt                       # Log files
│   ├── *.json                      # JSON output files
│   └── api_logs_debug/             # Debug log directory
│
├── bin/                             # 🚀 Executable Scripts
│   ├── start_server.sh             # Start the API server
│   ├── run_tests.sh                # Run all tests
│   ├── install_deps.sh             # Install dependencies
│   ├── manage_tokens.sh            # Token management
│   ├── update-version.sh           # Version update script
│   ├── upload-pip.sh               # Upload to PyPI
│   └── upload-test-pip.sh          # Upload to test PyPI
│
├── README.md                       # 📖 Main project documentation
├── LICENSE                         # 📄 License file
└── verify_structure.py            # 🔍 Structure verification script
```

## 📂 Directory Details

### `py_qj_robots/` - Core SDK Package
The main Python package containing the core functionality:

- **`perception.py`**: Main perception API with all 7 perception functions
- **`authorization.py`**: Handles authentication and access token management
- **`openai_api.py`**: OpenAI compatibility layer with chat completion interface
- **`__init__.py`**: Exports main classes (`Perception`, `OpenAIPerceptionAPI`)

### `docs/` - Documentation
All documentation files organized by topic:

- **API Guides**: Comprehensive guides for different API usage patterns
- **Setup Guides**: Installation and configuration instructions
- **Reference**: Technical documentation and project structure

### `tests/` - Test Scripts
Comprehensive test suite covering all functionality:

- **Unit Tests**: `test_openai_api.py`, `test_token_auth.py`
- **Integration Tests**: `verify_installation.py`
- **Performance Tests**: Various performance and stress testing scripts
- **Validation Tests**: QPS validation and concurrency testing

### `examples/` - Example Scripts
Practical examples demonstrating different usage patterns:

- **Direct API Usage**: How to use the SDK directly
- **OpenAI Client Usage**: Using standard OpenAI client libraries
- **Async Patterns**: Asynchronous usage examples

### `utils/` - Utility Scripts
Administrative and management tools:

- **Token Management**: CLI tools for managing authentication tokens
- **Installation**: Automated dependency installation and verification
- **Configuration**: Token configuration and mapping utilities

## 🔧 Key Files

### Core Application Files
- **`openai_api_server.py`**: FastAPI server providing OpenAI-compatible REST API
- **`token_mapping.json`**: Configuration file mapping tokens to credentials

### Configuration Files
- **`requirements.txt`**: Basic dependencies for core SDK
- **`requirements-openai.txt`**: Complete dependencies including FastAPI server
- **`setup.py`**: Package configuration with optional dependencies

### Documentation
- **`README.md`**: Main project overview and quick start guide
- **`LICENSE`**: MIT license file

## 🚀 Usage Patterns

### Development Workflow
1. **Install Dependencies**: `python utils/install_dependencies.py`
2. **Configure Tokens**: `python utils/manage_tokens.py add APP_ID APP_SECRET`
3. **Run Tests**: `python tests/test_openai_api.py`
4. **Start Server**: `python openai_api_server.py`
5. **Try Examples**: `python examples/openai_api_example.py`

### Import Patterns
```python
# Core SDK
from py_qj_robots import Perception, OpenAIPerceptionAPI

# Utilities (when needed)
from utils.token_config import TokenManager

# Examples and tests import from parent directory
import sys, os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
```

## 📋 File Categories

### 🏗️ Core Implementation
- `py_qj_robots/` package
- `openai_api_server.py`

### 📚 Documentation
- All `.md` files in `docs/`
- `README.md`

### 🧪 Testing & Validation
- All files in `tests/`
- Performance and stress testing scripts

### 💡 Examples & Demos
- All files in `examples/`
- Usage demonstration scripts

### 🛠️ Tools & Utilities
- All files in `utils/`
- Management and configuration tools

### 📦 Configuration
- `requirements*.txt`
- `setup.py`
- `token_mapping.json`

## 🔄 Migration Notes

### Import Path Changes
After reorganization, some import paths have changed:

**Before:**
```python
from token_config import TokenManager
```

**After:**
```python
from utils.token_config import TokenManager
```

### File Locations
- Documentation: Root → `docs/`
- Tests: Root → `tests/`
- Utilities: Root → `utils/`
- Examples: Root → `examples/`

## 🎯 Benefits of New Structure

1. **🧹 Clean Organization**: Files grouped by purpose and functionality
2. **📚 Better Documentation**: All docs in one place with clear navigation
3. **🧪 Isolated Testing**: Test files separated from implementation
4. **💡 Clear Examples**: Examples grouped and easily discoverable
5. **🛠️ Utility Management**: Tools and utilities in dedicated directory
6. **📦 Package Structure**: Follows Python packaging best practices
7. **🔍 Easy Navigation**: Logical directory structure for development

## 🚀 Getting Started

With the new structure:

1. **Read Documentation**: Start with `README.md`, then explore `docs/`
2. **Install Dependencies**: Use `utils/install_dependencies.py`
3. **Run Tests**: Execute scripts in `tests/` directory
4. **Try Examples**: Run scripts in `examples/` directory
5. **Use Utilities**: Manage tokens with `utils/manage_tokens.py`

The reorganized structure makes the project more professional, maintainable, and easier to navigate for both development and usage! 🎉
