# 感知API可视化测试工具使用指南

## 🎯 功能概述

我们已经创建了一个基于jQuery的H5网页，用于可视化测试感知API。这个工具提供了完整的聊天界面，支持图片上传、文本输入、流式/阻塞输出等功能。

## 🚀 访问方式

启动服务器后，通过以下URL访问demo页面：

```
http://localhost:8000/demo.html
```

## 🔧 功能特性

### 1. 聊天管理
- ✅ **新建聊天**: 点击"新建聊天"按钮创建新的对话
- ✅ **聊天历史**: 左侧显示所有聊天记录，支持切换
- ✅ **自动保存**: 聊天记录自动保存到浏览器本地存储

### 2. 模型配置
- ✅ **模型选择**: 当前支持 `qj-perception-v1` 模型
- ✅ **输出方式**: 
  - 阻塞模式：一次性返回完整结果
  - 流式模式：实时流式输出结果
- ✅ **API配置**: 可配置API Key和Base URL

### 3. 图片上传
- ✅ **多种上传方式**: 
  - 点击上传区域选择文件
  - 拖拽图片到上传区域
- ✅ **格式支持**: JPG, PNG, GIF等常见图片格式
- ✅ **预览功能**: 上传后立即显示图片预览
- ✅ **移除功能**: 可以移除已上传的图片
- 🔄 **COS上传**: 预留腾讯云COS上传接口（待实现）

### 4. 文本输入
- ✅ **多行输入**: 支持多行文本输入
- ✅ **快捷发送**: Enter键发送，Shift+Enter换行
- ✅ **智能按钮**: 根据输入内容自动启用/禁用发送按钮

### 5. 消息显示
- ✅ **聊天气泡**: 用户和AI消息使用不同样式
- ✅ **图片显示**: 消息中的图片正确显示
- ✅ **格式化输出**: 支持基本的markdown格式
- ✅ **流式效果**: 流式模式下显示打字机效果

## 📱 界面布局

### 左侧边栏
```
┌─────────────────────┐
│ 🤖 感知API测试      │
│ [+ 新建聊天]        │
├─────────────────────┤
│ 聊天历史            │
│ • 对话1             │
│ • 对话2             │
│ • ...               │
├─────────────────────┤
│ 配置                │
│ • 模型选择          │
│ • 输出方式          │
│ • API配置           │
└─────────────────────┘
```

### 主聊天区域
```
┌─────────────────────────────────┐
│ 聊天消息区域                    │
│ ┌─────────────────────────────┐ │
│ │ 用户: 这张图片中有几辆车？  │ │
│ │ [图片预览]                  │ │
│ └─────────────────────────────┘ │
│ ┌─────────────────────────────┐ │
│ │ 🤖: 🎉 太棒了，我已经帮你   │ │
│ │ 分析了图片中的内容！        │ │
│ │ ...                         │ │
│ └─────────────────────────────┘ │
├─────────────────────────────────┤
│ 输入区域                        │
│ ┌─────────────────────────────┐ │
│ │ [图片上传区域]              │ │
│ └─────────────────────────────┘ │
│ ┌─────────────────────────────┐ │
│ │ 请输入您的问题...    [发送] │ │
│ └─────────────────────────────┘ │
└─────────────────────────────────┘
```

## 🧪 使用步骤

### 1. 启动服务器
```bash
cd openai-server
python openai_api_server.py
```

### 2. 访问demo页面
在浏览器中打开：`http://localhost:8000/demo.html`

### 3. 配置API
- 确认API Key已正确设置
- 确认Base URL为 `http://localhost:8000`

### 4. 开始测试
1. 点击"新建聊天"创建新对话
2. 上传图片（点击或拖拽）
3. 输入问题文本
4. 选择输出方式（阻塞/流式）
5. 点击发送按钮

### 5. 查看结果
- 阻塞模式：等待完整结果一次性显示
- 流式模式：观察实时流式输出效果

## 🎨 界面特色

### 响应式设计
- 支持桌面和移动设备
- 自适应屏幕尺寸

### 用户体验
- 流畅的动画效果
- 直观的操作反馈
- 清晰的状态指示

### 视觉设计
- 现代化的UI设计
- 清晰的信息层次
- 舒适的色彩搭配

## 🔧 技术实现

### 前端技术栈
- **HTML5**: 语义化标签
- **CSS3**: 现代样式和动画
- **jQuery**: DOM操作和事件处理
- **Bootstrap 5**: 响应式布局
- **Font Awesome**: 图标库

### 核心功能
- **文件上传**: File API + 拖拽支持
- **流式处理**: Fetch API + ReadableStream
- **本地存储**: localStorage持久化
- **实时更新**: 动态DOM操作

### API集成
- **OpenAI兼容**: 完全兼容OpenAI API格式
- **流式支持**: Server-Sent Events处理
- **错误处理**: 完善的异常处理机制

## 🚀 扩展功能

### 即将实现
- 🔄 **腾讯云COS上传**: 真实的图片上传功能
- 📊 **使用统计**: API调用统计和分析
- 🎨 **主题切换**: 明暗主题切换
- 📱 **PWA支持**: 离线使用能力

### 可扩展性
- 模块化设计，易于添加新功能
- 插件化架构，支持自定义扩展
- 配置化界面，支持个性化设置

## 🎉 总结

这个可视化测试工具为感知API提供了完整的测试界面，具有以下优势：

1. **功能完整**: 涵盖所有API测试需求
2. **界面友好**: 现代化的用户界面
3. **操作简单**: 直观的交互设计
4. **扩展性强**: 易于添加新功能
5. **兼容性好**: 支持多种设备和浏览器

通过这个工具，您可以轻松测试感知API的各种功能，验证图像识别效果，体验流式输出的魅力！
