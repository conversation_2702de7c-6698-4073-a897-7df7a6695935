# Nginx 部署和配置指南

## 🎯 配置概述

本指南说明如何配置 nginx 将 `jwd.vooice.tech` 域名的 `/v1` 路径转发到本地 OpenAI API 服务器。

### 📋 配置详情

- **域名**: `jwd.vooice.tech`
- **SSL**: 启用 (端口 443)
- **转发路径**: `/v1/*`
- **目标服务器**: `http://localhost:8000/v1/`
- **OpenAI API 服务器端口**: 8000

## 🔧 配置文件

### Nginx 配置 (`conf/nginx.conf`)

关键配置段：

```nginx
server {
    listen       443 ssl;
    server_name  localhost jwd.vooice.tech;

    # SSL 证书配置
    ssl_certificate      /Users/<USER>/Downloads/fullchain.pem;
    ssl_certificate_key  /Users/<USER>/Downloads/privkey.pem;

    # OpenAI API 转发配置
    location /v1/ {
        # CORS 配置
        set $cors_origin "";
        if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
            set $cors_origin $http_origin;
        }
        
        # 预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' $cors_origin always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
            add_header 'Access-Control-Max-Age' 86400 always;
            add_header 'Content-Length' 0;
            return 204;
        }

        # 常规请求处理
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        # 转发到本地 OpenAI API 服务器
        proxy_pass http://localhost:8000/v1/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置 - 适合 AI API 的较长响应时间
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;  # 5分钟
    }
}
```

## 🚀 部署步骤

### 1. 启动 OpenAI API 服务器

```bash
# 方法 1: 使用启动脚本
./bin/start_server.sh

# 方法 2: 直接运行
python openai-server/openai_api_server.py

# 方法 3: 后台运行
nohup python openai-server/openai_api_server.py > logs/openai_server.log 2>&1 &
```

### 2. 验证本地服务器

```bash
# 检查服务器是否运行
curl http://localhost:8000/health

# 检查 API 端点
curl http://localhost:8000/v1/models
```

### 3. 重启 Nginx

```bash
# 方法 1: 使用重启脚本
./bin/restart_nginx.sh

# 方法 2: 手动重启
nginx -t -c $(pwd)/conf/nginx.conf  # 检查配置
nginx -s reload -c $(pwd)/conf/nginx.conf  # 重新加载配置
```

### 4. 测试域名转发

```bash
# 测试健康检查
curl -k https://jwd.vooice.tech/health

# 测试 API 端点
curl -k https://jwd.vooice.tech/v1/models

# 测试 OpenAI API
curl -k -X POST https://jwd.vooice.tech/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc" \
  -d '{
    "model": "qj-perception-v1",
    "messages": [{"role": "user", "content": "Hello"}],
    "max_tokens": 100
  }'
```

## 🧪 测试脚本

### 自动化测试

```bash
# 运行完整的配置测试
python test_nginx_config.py

# 测试视觉 API
python test_openai_vision_api.py test_images/cars_street.jpg

# 快速测试
python quick_vision_test.py test_images/red_car.jpg
```

## 🔍 故障排除

### 常见问题

1. **502 Bad Gateway**
   - 检查 OpenAI API 服务器是否在端口 8000 运行
   - 检查防火墙设置

2. **SSL 证书错误**
   - 确认证书文件路径正确
   - 检查证书是否过期

3. **CORS 错误**
   - 检查 CORS 配置是否包含您的域名
   - 确认预检请求处理正确

### 调试命令

```bash
# 检查端口占用
lsof -i :8000
netstat -an | grep 8000

# 检查 nginx 进程
ps aux | grep nginx

# 查看 nginx 错误日志
tail -f /usr/local/var/log/nginx/error.log

# 查看 OpenAI API 服务器日志
tail -f logs/openai_server.log
```

## 📊 监控和日志

### 日志位置

- **Nginx 访问日志**: `/usr/local/var/log/nginx/access.log`
- **Nginx 错误日志**: `/usr/local/var/log/nginx/error.log`
- **OpenAI API 服务器日志**: 控制台输出或 `logs/openai_server.log`

### 监控脚本

```bash
# 监控服务状态
watch -n 5 'curl -s http://localhost:8000/health && echo " - Local OK" || echo " - Local FAIL"; curl -sk https://jwd.vooice.tech/health && echo " - Domain OK" || echo " - Domain FAIL"'
```

## 🔒 安全注意事项

1. **SSL 证书**: 确保使用有效的 SSL 证书
2. **访问控制**: 考虑添加 IP 白名单或其他访问控制
3. **API 密钥**: 保护好 API 密钥，不要在日志中暴露
4. **防火墙**: 确保只开放必要的端口

## 📝 维护

### 定期检查

- SSL 证书到期时间
- 服务器资源使用情况
- 日志文件大小
- API 响应时间

### 备份

- 定期备份 nginx 配置文件
- 备份 SSL 证书
- 备份 token 配置文件
