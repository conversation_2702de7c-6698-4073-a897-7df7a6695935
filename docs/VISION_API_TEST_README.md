# OpenAI Vision API 测试指南

本目录包含用于测试 OpenAI Vision API 的脚本，专门用于识别图片中的车辆数量和颜色。

## 🔧 重要更新

为了避免与标准 OpenAI 库的导入冲突，我们已将 `openai/` 目录重命名为 `openai-server/`。现在您可以正常使用标准的 OpenAI 库进行测试。

## 📁 文件说明

- `test_openai_vision_api.py` - 完整的测试脚本，支持普通和流式 API
- `quick_vision_test.py` - 简化版测试脚本，快速测试
- `download_test_images.py` - 下载测试图片的脚本
- `VISION_API_TEST_README.md` - 本说明文档

## 🔧 配置信息

- **API 端点**: `https://jwd.vooice.tech/v1`
- **访问凭证**: `sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc`
- **模型**: `qj-perception-v1`

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install openai requests
```

### 2. 下载测试图片（可选）

```bash
python download_test_images.py
```

### 3. 运行测试

#### 使用完整测试脚本：

```bash
# 测试本地图片
python test_openai_vision_api.py ./test_images/cars_street.jpg

# 测试网络图片
python test_openai_vision_api.py https://example.com/car_image.jpg
```

#### 使用快速测试脚本：

```bash
# 快速测试
python quick_vision_test.py ./test_images/red_car.jpg
```

## 📝 测试内容

脚本会向 API 发送以下问题：
> "请分析这张图片，告诉我图中有几辆车，分别是什么颜色？"

## 🎯 预期输出

### 完整测试脚本输出示例：

```
🚀 开始测试 OpenAI Vision API...
📍 API 端点: https://jwd.vooice.tech/v1
🖼️  图片路径: ./test_images/cars_street.jpg
--------------------------------------------------
📝 正在编码图片...
✅ 图片编码成功
🔍 正在调用 API 分析图片...
✅ API 调用成功！
--------------------------------------------------
🤖 AI 分析结果:
我可以看到这张图片中有3辆车：

1. 一辆红色的轿车，位于图片左侧
2. 一辆蓝色的SUV，位于图片中央
3. 一辆白色的小型车，位于图片右侧

所有车辆都停在街道上，图片拍摄角度良好，车辆特征清晰可见。
--------------------------------------------------
📊 使用统计:
   输入 tokens: 1250
   输出 tokens: 85
   总计 tokens: 1335
```

### 快速测试脚本输出示例：

```
🔍 分析图片: ./test_images/red_car.jpg
🤖 分析结果:
图片中有1辆车，是红色的。这是一辆红色轿车，外观整洁，停在路边。
```

## 🔍 支持的图片格式

- **本地图片**: JPG, JPEG, PNG, GIF, BMP, WEBP
- **网络图片**: 支持 HTTP/HTTPS URL

## 🛠️ 故障排除

### 常见问题：

1. **API 调用失败**
   - 检查网络连接
   - 确认 API 端点是否可访问
   - 验证访问凭证是否正确

2. **图片编码失败**
   - 确认图片文件存在且可读
   - 检查图片格式是否支持
   - 对于网络图片，确认 URL 可访问

3. **依赖包缺失**
   ```bash
   pip install openai requests
   ```

### 调试模式：

在脚本中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 API 功能测试

脚本测试以下 API 功能：

1. **图像上传和编码** - Base64 编码
2. **视觉识别** - 车辆检测和颜色识别
3. **普通响应** - 一次性返回完整结果
4. **流式响应** - 实时流式返回结果
5. **错误处理** - 网络错误和 API 错误处理

## 🔐 安全说明

- 访问凭证已从 `conf/token_mapping.json` 中选取
- 请勿在生产环境中硬编码 API 密钥
- 建议使用环境变量存储敏感信息

## 📞 技术支持

如果遇到问题，请检查：

1. API 服务是否正常运行
2. 网络连接是否稳定
3. 图片文件是否有效
4. 依赖包是否正确安装

---

**注意**: 这些脚本专门用于测试车辆识别功能，您可以根据需要修改问题内容来测试其他视觉识别功能。
