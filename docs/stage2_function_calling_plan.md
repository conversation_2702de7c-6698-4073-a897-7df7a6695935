# 阶段2: OpenAI Function Calling 标准实现计划

## 🎯 目标概述

实现完整的OpenAI Function Calling标准支持，使感知API完全兼容OpenAI的函数调用规范。

## 📋 实现任务清单

### 2.1 请求模型扩展 ✅
- [x] 扩展ChatCompletionRequest支持functions参数
- [x] 扩展ChatCompletionRequest支持function_call参数
- [x] 添加函数参数验证

### 2.2 函数定义标准化 🔄
- [ ] 创建标准的函数定义结构
- [ ] 实现7个感知函数的OpenAI格式定义
- [ ] 添加参数验证和类型检查

### 2.3 函数调用响应格式 🔄
- [ ] 实现标准的function_call响应格式
- [ ] 支持流式函数调用响应
- [ ] 添加函数执行结果处理

### 2.4 智能函数调用逻辑 🔄
- [ ] 实现function_call="auto"模式
- [ ] 实现function_call={"name": "function_name"}模式
- [ ] 实现function_call="none"模式

### 2.5 多轮对话支持 🔄
- [ ] 支持函数调用后的结果反馈
- [ ] 实现基于函数结果的后续响应
- [ ] 支持多个函数的组合调用

## 🏗️ OpenAI Function Calling 标准

### 标准请求格式
```json
{
  "model": "qj-perception-v1",
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "分析这张图片中的汽车"},
        {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
      ]
    }
  ],
  "functions": [
    {
      "name": "check_image",
      "description": "检测和定位图像中的对象",
      "parameters": {
        "type": "object",
        "properties": {
          "image_url": {"type": "string", "description": "图像URL"},
          "object_names": {"type": "array", "items": {"type": "string"}}
        },
        "required": ["image_url", "object_names"]
      }
    }
  ],
  "function_call": "auto"
}
```

### 标准响应格式

#### 函数调用响应
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "qj-perception-v1",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": null,
      "function_call": {
        "name": "check_image",
        "arguments": "{\"image_url\":\"https://example.com/car.jpg\",\"object_names\":[\"car\"]}"
      }
    },
    "finish_reason": "function_call"
  }]
}
```

#### 函数结果处理响应
```json
{
  "id": "chatcmpl-124", 
  "object": "chat.completion",
  "created": 1677652289,
  "model": "qj-perception-v1",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "我在图片中检测到了一辆汽车，位置在坐标(100, 200)，置信度为0.95。"
    },
    "finish_reason": "stop"
  }]
}
```

## 🔧 实现架构

### 2.1 函数调用流程
```
1. 用户发送请求 + 函数定义
   ↓
2. 智能判断是否需要调用函数
   ↓
3a. 需要调用 → 返回function_call响应
   ↓
4a. 客户端发送函数执行结果
   ↓
5a. 基于结果生成最终回复

3b. 不需要调用 → 直接生成回复
```

### 2.2 核心组件扩展

#### FunctionCallManager (新增)
```python
class FunctionCallManager:
    """函数调用管理器"""
    
    def should_call_function(self, messages, functions, function_call) -> bool:
        """判断是否需要调用函数"""
    
    def select_function(self, messages, functions, function_call) -> tuple:
        """选择要调用的函数"""
    
    def extract_function_arguments(self, messages, function_def) -> dict:
        """提取函数参数"""
    
    def validate_function_call(self, function_name, arguments, functions) -> bool:
        """验证函数调用"""
```

#### PerceptionHandler (扩展)
```python
class PerceptionHandler:
    """感知处理器 - 扩展Function Calling支持"""
    
    def get_function_definitions(self) -> List[Dict]:
        """获取标准化的函数定义"""
    
    def execute_function_call(self, function_name, arguments) -> Any:
        """执行函数调用"""
    
    def format_function_result(self, result, function_name) -> str:
        """格式化函数执行结果"""
```

### 2.3 支持的函数调用模式

#### 1. function_call="auto" (智能模式)
- 系统自动判断是否需要调用函数
- 基于用户输入和可用函数智能选择

#### 2. function_call={"name": "function_name"} (指定模式)
- 强制调用指定的函数
- 用于明确的函数调用需求

#### 3. function_call="none" (禁用模式)
- 禁用函数调用，直接生成文本回复
- 用于纯文本对话场景

## 📊 7个感知函数的标准定义

### 1. check_image - 对象检测
```json
{
  "name": "check_image",
  "description": "检测和定位图像中的对象，返回对象位置、类别和置信度",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {
        "type": "array", 
        "items": {"type": "string"},
        "description": "要检测的对象名称列表，如['car', 'person', 'tree']"
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 2. split_image - 图像分割
```json
{
  "name": "split_image",
  "description": "对图像进行语义分割，将不同对象分离出来",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分割的图像URL"},
      "object_names": {
        "type": "array",
        "items": {"type": "string"}, 
        "description": "要分割的对象类别"
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 3. props_describe - 属性描述
```json
{
  "name": "props_describe", 
  "description": "详细描述图像中对象的属性特征",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {
        "type": "array",
        "items": {"type": "string"},
        "description": "要描述的对象名称"
      },
      "questions": {
        "type": "array",
        "items": {"type": "string"},
        "description": "具体要回答的问题",
        "default": ["描述这个对象的详细特征"]
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 4. angle_prediction - 角度预测
```json
{
  "name": "angle_prediction",
  "description": "预测图像中对象的角度和方向信息",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {
        "type": "array",
        "items": {"type": "string"},
        "description": "要分析角度的对象名称"
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 5. key_point_prediction - 关键点预测
```json
{
  "name": "key_point_prediction",
  "description": "预测图像中对象的关键点位置",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {
        "type": "array",
        "items": {"type": "string"},
        "description": "要分析关键点的对象名称"
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 6. grab_point_prediction - 抓取点预测
```json
{
  "name": "grab_point_prediction",
  "description": "为机器人预测最佳的对象抓取点",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {
        "type": "array",
        "items": {"type": "string"},
        "description": "要分析抓取点的对象名称"
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 7. full_perception - 综合感知
```json
{
  "name": "full_perception",
  "description": "对图像进行全面的感知分析，包括检测、分割、描述等",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {
        "type": "array",
        "items": {"type": "string"},
        "description": "要分析的对象名称"
      },
      "questions": {
        "type": "array",
        "items": {"type": "string"},
        "description": "要回答的具体问题",
        "default": ["提供全面的图像分析"]
      }
    },
    "required": ["image_url", "object_names"]
  }
}
```

## 🧪 测试策略

### 2.1 单元测试
- 函数定义验证
- 参数提取和验证
- 函数调用响应格式

### 2.2 集成测试
- 完整的函数调用流程
- 多轮对话支持
- 错误处理机制

### 2.3 兼容性测试
- OpenAI客户端库兼容性
- 标准Function Calling工具兼容性

## 📈 预期收益

1. **✅ 标准兼容**: 完全符合OpenAI Function Calling规范
2. **✅ 智能选择**: 自动判断何时调用哪个函数
3. **✅ 多轮对话**: 支持基于函数结果的后续交互
4. **✅ 工具集成**: 与现有OpenAI工具无缝集成
5. **✅ 扩展性强**: 易于添加新的感知函数

## 🎯 成功标准

阶段2完成后应该能够：

1. **处理标准Function Calling请求**
2. **返回标准Function Calling响应**
3. **支持所有3种函数调用模式**
4. **通过OpenAI客户端库测试**
5. **支持多轮函数调用对话**

让我们开始实现这些功能！🚀
