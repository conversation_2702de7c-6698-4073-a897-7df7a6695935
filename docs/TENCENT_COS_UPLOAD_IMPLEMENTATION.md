# 腾讯云COS图片上传功能实现

## 🎯 功能概述

实现了完整的图片上传到腾讯云COS的流程，用户选择图片后，点击发送时会自动：
1. 获取上传参数
2. 上传图片到腾讯云COS
3. 构建最终图片URL
4. 发送消息到感知API

## 🔧 实现流程

### 1. 用户操作流程
```
用户点击图片按钮 → 选择图片 → 输入问题 → 点击发送
    ↓
显示"正在上传图片到云端..." → 上传完成 → 发送消息到API
```

### 2. 技术实现流程
```
1. handleImageFile() - 处理用户选择的图片
   ├── 保存文件对象到 currentImageFile
   ├── 创建本地预览URL到 currentImageUrl
   └── 显示预览界面

2. sendMessage() - 用户点击发送时
   ├── 检查是否有图片
   ├── 调用 uploadImageToCOS() 上传图片
   └── 使用云端URL发送消息

3. uploadImageToCOS() - 上传图片到腾讯云COS
   ├── 调用 getUploadParams() 获取上传参数
   ├── 调用 uploadFileToCOS() 上传文件
   └── 构建最终图片URL

4. getUploadParams() - 获取上传参数
   └── GET https://uat-open.qj-robots.com/open-api//open-apis/base/upload/params

5. uploadFileToCOS() - 上传文件到COS
   └── POST 到腾讯云COS URL，期望返回204状态码
```

## 📝 核心代码实现

### 1. 全局变量
```javascript
let currentImageUrl = null;    // 本地预览URL
let currentImageFile = null;   // 原始文件对象
```

### 2. 图片选择处理
```javascript
function handleImageFile(file) {
    if (!file.type.startsWith('image/')) {
        showToast('文件类型错误', '请选择图片文件！', 'error');
        return;
    }

    // 保存文件对象，用于后续上传
    currentImageFile = file;

    // 显示预览
    const reader = new FileReader();
    reader.onload = function (e) {
        $('#previewImage').attr('src', e.target.result);
        $('#uploadedImagePreview').show();
        $('#uploadStatus').hide();
        updateSendButton();
    };
    reader.readAsDataURL(file);

    // 创建本地预览URL
    currentImageUrl = URL.createObjectURL(file);
    
    showToast('图片已选择', '图片已准备就绪，点击发送将自动上传到云端');
}
```

### 3. 发送消息逻辑
```javascript
async function sendMessage() {
    // ... 前置检查 ...
    
    let finalImageUrl = null;
    
    try {
        // 如果有图片，先上传到腾讯云COS
        if (hasImage) {
            showToast('上传中', '正在上传图片到云端...', 'info');
            finalImageUrl = await uploadImageToCOS();
            showToast('上传成功', '图片已成功上传到云端');
        }
        
        // 使用云端URL发送消息
        // ...
    } catch (error) {
        // 错误处理
    }
}
```

### 4. 腾讯云COS上传
```javascript
async function uploadImageToCOS() {
    if (!currentImageFile) {
        throw new Error('没有选择图片文件');
    }
    
    try {
        // 1. 获取上传参数
        const uploadParams = await getUploadParams();
        
        // 2. 上传图片到腾讯云COS
        await uploadFileToCOS(currentImageFile, uploadParams.url);
        
        // 3. 构建最终的图片URL
        const finalImageUrl = `https://cos-cdn-v1.qj-robots.com/${uploadParams.key}`;
        
        return finalImageUrl;
    } catch (error) {
        console.error('图片上传失败:', error);
        throw new Error(`图片上传失败: ${error.message}`);
    }
}

async function getUploadParams() {
    const response = await fetch('https://uat-open.qj-robots.com/open-api//open-apis/base/upload/params', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    });
    
    if (!response.ok) {
        throw new Error(`获取上传参数失败: HTTP ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.code !== 0) {
        throw new Error(`获取上传参数失败: ${result.message}`);
    }
    
    return result.data;
}

async function uploadFileToCOS(file, uploadUrl) {
    const response = await fetch(uploadUrl, {
        method: 'POST',
        body: file,
        headers: {
            'Content-Type': file.type
        }
    });
    
    // 腾讯云COS上传成功返回204状态码
    if (response.status !== 204) {
        throw new Error(`文件上传失败: HTTP ${response.status}`);
    }
    
    return true;
}
```

## 🌐 API接口详情

### 1. 获取上传参数接口
- **URL**: `https://uat-open.qj-robots.com/open-api//open-apis/base/upload/params`
- **方法**: GET
- **返回格式**:
```json
{
    "code": 0,
    "data": {
        "url": "https://qj-prod-1357102482.cos.ap-beijing.myqcloud.com/temp/xxx?sign=...",
        "key": "temp/xxx"
    },
    "message": "OK"
}
```

### 2. 腾讯云COS上传接口
- **URL**: 从上传参数接口获取的 `data.url`
- **方法**: POST
- **请求体**: 原始文件数据
- **请求头**: `Content-Type: image/jpeg` (根据文件类型)
- **成功状态码**: 204

### 3. 最终图片URL构建
- **格式**: `https://cos-cdn-v1.qj-robots.com/{key}`
- **示例**: `https://cos-cdn-v1.qj-robots.com/temp/xxx`

## 🎨 用户体验优化

### 1. 状态提示
- ✅ **图片选择**: "图片已选择，图片已准备就绪，点击发送将自动上传到云端"
- ✅ **上传中**: "正在上传图片到云端..."
- ✅ **上传成功**: "图片已成功上传到云端"
- ✅ **上传失败**: "图片上传失败: {错误信息}"

### 2. 界面反馈
- ✅ 图片预览立即显示
- ✅ 上传状态通过Toast通知
- ✅ 发送按钮状态管理
- ✅ 错误处理和用户提示

### 3. 内存管理
- ✅ 使用 `URL.revokeObjectURL()` 清理本地预览URL
- ✅ 移除图片时清理所有相关状态
- ✅ 避免内存泄漏

## 🔍 错误处理

### 1. 文件类型检查
```javascript
if (!file.type.startsWith('image/')) {
    showToast('文件类型错误', '请选择图片文件！', 'error');
    return;
}
```

### 2. 网络请求错误
```javascript
if (!response.ok) {
    throw new Error(`获取上传参数失败: HTTP ${response.status}`);
}
```

### 3. 业务逻辑错误
```javascript
if (result.code !== 0) {
    throw new Error(`获取上传参数失败: ${result.message}`);
}
```

### 4. 上传状态检查
```javascript
if (response.status !== 204) {
    throw new Error(`文件上传失败: HTTP ${response.status}`);
}
```

## 🧪 测试验证

### 1. 上传参数接口测试
```bash
curl 'https://uat-open.qj-robots.com/open-api//open-apis/base/upload/params'
```
**预期结果**: 返回包含 `url` 和 `key` 的JSON响应

### 2. 完整流程测试
1. 打开 `http://localhost:8000/demo.html`
2. 点击图片上传按钮
3. 选择图片文件
4. 输入问题
5. 点击发送
6. 观察上传进度和最终结果

## 🎉 功能特点

1. **✅ 完整流程**: 从选择图片到发送消息的完整自动化流程
2. **✅ 错误处理**: 完善的错误处理和用户提示
3. **✅ 状态管理**: 清晰的状态管理和界面反馈
4. **✅ 内存优化**: 避免内存泄漏的资源管理
5. **✅ 用户体验**: 直观的操作流程和及时的状态反馈

现在用户可以无缝地上传图片并与感知API进行交互了！🚀
