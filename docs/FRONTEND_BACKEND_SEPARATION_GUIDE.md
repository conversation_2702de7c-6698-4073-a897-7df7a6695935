# 前后端分离架构指南

## 🎯 架构概述

实现了完整的前后端分离架构，将静态文件（HTML、CSS、JS）与API服务分离：

- **前端**: 静态文件由nginx直接提供服务
- **后端**: API请求通过nginx代理转发到8000端口的OpenAI兼容服务

## 🏗️ 架构设计

### 1. 目录结构
```
/opt/homebrew/var/www/perception/
├── demo.html          # 主页面
├── demo.css           # 样式文件
└── demo.js            # 脚本文件
```

### 2. URL路径映射

| 访问路径 | 服务方式 | 实际位置 |
|---------|---------|---------|
| `https://jwd.vooice.tech/perception/demo.html` | nginx静态文件 | `/opt/homebrew/var/www/perception/demo.html` |
| `https://jwd.vooice.tech/perception/demo.css` | nginx静态文件 | `/opt/homebrew/var/www/perception/demo.css` |
| `https://jwd.vooice.tech/perception/demo.js` | nginx静态文件 | `/opt/homebrew/var/www/perception/demo.js` |
| `https://jwd.vooice.tech/openai/v1/chat/completions` | nginx代理 | `http://localhost:8000/v1/chat/completions` |
| `https://jwd.vooice.tech/openai/v1/models` | nginx代理 | `http://localhost:8000/v1/models` |

## 🔧 Nginx配置详解

### 1. 静态文件服务配置

```nginx
# 感知API测试页面静态文件配置（前后端分离）
location /perception/ {
    # 直接提供静态文件服务
    alias /opt/homebrew/var/www/perception/;
    index demo.html;
    
    # 设置静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 设置HTML文件缓存策略
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }
}
```

**特点**:
- ✅ 使用 `alias` 指令直接提供静态文件
- ✅ 设置了合理的缓存策略
- ✅ CSS/JS文件缓存1年，HTML文件缓存1小时

### 2. API代理配置

```nginx
# OpenAI API接口转发配置
location /openai/ {
    # CORS 配置
    set $cors_origin "";
    if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
        set $cors_origin $http_origin;
    }

    # 预检请求处理
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
        add_header 'Access-Control-Max-Age' 86400 always;
        add_header 'Content-Length' 0;
        return 204;
    }

    # 常规请求处理
    add_header 'Access-Control-Allow-Origin' $cors_origin always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;

    # 🔑 关键：去掉 /openai 前缀，转发到本地OpenAI API服务器
    rewrite ^/openai/(.*)$ /$1 break;
    proxy_pass http://localhost:8000;
    
    # proxy设置...
}
```

**特点**:
- ✅ 完整的CORS支持
- ✅ 去掉 `/openai` 前缀后转发
- ✅ 适合AI API的长超时设置

## 📝 前端代码修改

### 1. HTML文件修改

**原始路径**:
```html
<link href="/static/demo.css" rel="stylesheet">
<script src="/static/demo.js"></script>
```

**修改后**:
```html
<link href="demo.css" rel="stylesheet">
<script src="demo.js"></script>
```

**说明**: 使用相对路径，文件在同一目录下

### 2. JavaScript API配置修改

**Base URL自动检测**:
```javascript
function getBaseUrl() {
    const inputValue = $('#baseUrl').val();
    if (inputValue) {
        return inputValue;
    }
    
    // 根据当前域名自动判断API地址
    if (window.location.hostname === 'jwd.vooice.tech') {
        return 'https://jwd.vooice.tech/openai';  // 🔑 新的API路径
    } else {
        return 'http://localhost:8000';
    }
}
```

**页面初始化**:
```javascript
function initializeApp() {
    // ... 其他代码 ...
    
    // 根据当前域名自动设置Base URL
    if (window.location.hostname === 'jwd.vooice.tech') {
        $('#baseUrl').val('https://jwd.vooice.tech/openai');
    } else {
        $('#baseUrl').val('http://localhost:8000');
    }
}
```

### 3. HTML默认值修改

```html
<input type="text" class="form-control form-control-sm" id="baseUrl"
       value="https://jwd.vooice.tech/openai" placeholder="API服务地址">
```

## 🚀 部署流程

### 1. 自动部署（推荐）

```bash
./deploy-frontend.sh
```

### 2. 手动部署

```bash
# 1. 创建静态文件目录
mkdir -p /opt/homebrew/var/www/perception

# 2. 复制静态文件
cp openai-server/static/* /opt/homebrew/var/www/perception/

# 3. 设置权限
chmod -R 644 /opt/homebrew/var/www/perception/*
chmod 755 /opt/homebrew/var/www/perception

# 4. 备份nginx配置
sudo cp /opt/homebrew/etc/nginx/nginx.conf /opt/homebrew/etc/nginx/nginx.conf.backup

# 5. 应用新配置
sudo cp conf/nginx.conf /opt/homebrew/etc/nginx/nginx.conf

# 6. 测试并重载
sudo nginx -t
sudo nginx -s reload
```

## 🧪 测试验证

### 1. 静态文件测试

```bash
# 测试主页面
curl -I https://jwd.vooice.tech/perception/demo.html
# 期望: HTTP/2 200, Content-Type: text/html

# 测试CSS文件
curl -I https://jwd.vooice.tech/perception/demo.css
# 期望: HTTP/2 200, Content-Type: text/css

# 测试JS文件
curl -I https://jwd.vooice.tech/perception/demo.js
# 期望: HTTP/2 200, Content-Type: application/javascript
```

### 2. API接口测试

```bash
# 测试聊天接口
curl -X POST https://jwd.vooice.tech/openai/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-test" \
  -d '{"model":"qj-perception-v1","messages":[{"role":"user","content":"test"}]}'

# 测试模型列表
curl https://jwd.vooice.tech/openai/v1/models
```

### 3. 浏览器测试

1. 访问 `https://jwd.vooice.tech/perception/demo.html`
2. 检查开发者工具，确认：
   - ✅ 所有静态资源正常加载（200状态）
   - ✅ 页面样式正常显示
   - ✅ JavaScript功能正常
   - ✅ API调用正常工作

## 🎯 架构优势

### 1. 性能优势
- ✅ **静态文件缓存**: CSS/JS文件缓存1年，减少重复请求
- ✅ **CDN友好**: 静态文件可以轻松接入CDN
- ✅ **并发处理**: nginx高效处理静态文件请求

### 2. 维护优势
- ✅ **职责分离**: 前端静态文件与后端API完全分离
- ✅ **独立部署**: 前端和后端可以独立更新部署
- ✅ **扩展性好**: 可以轻松添加多个前端应用

### 3. 安全优势
- ✅ **API隔离**: API服务不直接暴露静态文件
- ✅ **CORS控制**: 精确控制跨域访问
- ✅ **路径隔离**: 前端和API使用不同的URL路径

## 🔄 更新流程

### 1. 前端更新
```bash
# 修改源文件
vim openai-server/static/demo.html
vim openai-server/static/demo.css
vim openai-server/static/demo.js

# 重新部署
./deploy-frontend.sh
```

### 2. 后端更新
```bash
# 重启API服务
cd openai-server
python openai_api_server.py
```

## 🎉 总结

前后端分离架构实现了：

1. ✅ **静态文件直接服务**: 提高性能，减少服务器负载
2. ✅ **API路径优化**: 使用 `/openai/` 前缀，语义清晰
3. ✅ **缓存策略优化**: 合理的缓存设置提升用户体验
4. ✅ **部署流程简化**: 自动化部署脚本
5. ✅ **架构清晰**: 前后端职责分离，便于维护

现在您的感知API测试工具采用了现代化的前后端分离架构！🚀
