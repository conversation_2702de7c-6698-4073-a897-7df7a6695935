# Nginx 404错误修复指南

## 🚨 问题分析

当访问 `https://jwd.vooice.tech/perception/demo.html` 时，CSS和JS文件出现404错误。

### 问题原因
原始配置中的 `proxy_pass http://localhost:8000/;` 会导致路径重写问题：

```
请求: https://jwd.vooice.tech/perception/static/demo.css
转发: http://localhost:8000/static/demo.css  ❌ 错误！
```

实际上应该是：
```
请求: https://jwd.vooice.tech/perception/static/demo.css  
转发: http://localhost:8000/static/demo.css  ✅ 正确！
```

## 🔧 解决方案

### 方案1: 使用rewrite规则（推荐）

在nginx配置中的 `/perception/` location块中添加rewrite规则：

```nginx
location /perception/ {
    # CORS 配置
    set $cors_origin "";
    if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
        set $cors_origin $http_origin;
    }

    # 预检请求处理
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
        add_header 'Access-Control-Max-Age' 86400 always;
        add_header 'Content-Length' 0;
        return 204;
    }

    # 常规请求处理
    add_header 'Access-Control-Allow-Origin' $cors_origin always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;

    # 🔑 关键修复：去掉 /perception 前缀
    rewrite ^/perception/(.*)$ /$1 break;
    proxy_pass http://localhost:8000;
    
    # 其他proxy设置...
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;

    # 超时设置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 300s;
}
```

### 方案2: 使用alias指令（备选）

如果方案1不工作，可以尝试使用alias：

```nginx
location /perception/ {
    alias /path/to/openai-server/static/;
    try_files $uri @perception_proxy;
}

location @perception_proxy {
    # CORS和proxy配置
    rewrite ^/perception/(.*)$ /$1 break;
    proxy_pass http://localhost:8000;
    # ... 其他配置
}
```

## 🛠️ 修复步骤

### 1. 备份当前配置
```bash
sudo cp /opt/homebrew/etc/nginx/nginx.conf /opt/homebrew/etc/nginx/nginx.conf.backup
```

### 2. 编辑nginx配置
```bash
sudo nano /opt/homebrew/etc/nginx/nginx.conf
```

### 3. 找到HTTPS server块，添加/修改 `/perception/` location

在443端口的server块中添加或修改：

```nginx
server {
    listen       443 ssl;
    server_name  localhost jwd.vooice.tech;
    
    # SSL配置...
    
    # 感知API测试页面配置
    location /perception/ {
        # CORS 配置
        set $cors_origin "";
        if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
            set $cors_origin $http_origin;
        }

        # 预检请求处理
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' $cors_origin always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
            add_header 'Access-Control-Max-Age' 86400 always;
            add_header 'Content-Length' 0;
            return 204;
        }

        # 常规请求处理
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        # 🔑 关键修复：去掉 /perception 前缀
        rewrite ^/perception/(.*)$ /$1 break;
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;
    }
    
    # 其他location配置...
}
```

### 4. 测试配置
```bash
sudo nginx -t
```

### 5. 重新加载配置
```bash
sudo nginx -s reload
```

## 🧪 测试验证

### 1. 检查页面访问
```bash
curl -I https://jwd.vooice.tech/perception/demo.html
```
期望返回: `HTTP/2 200`

### 2. 检查CSS文件
```bash
curl -I https://jwd.vooice.tech/perception/static/demo.css
```
期望返回: `HTTP/2 200` 和 `Content-Type: text/css`

### 3. 检查JS文件
```bash
curl -I https://jwd.vooice.tech/perception/static/demo.js
```
期望返回: `HTTP/2 200` 和 `Content-Type: application/javascript`

### 4. 浏览器测试
1. 打开 `https://jwd.vooice.tech/perception/demo.html`
2. 打开浏览器开发者工具 (F12)
3. 查看Network标签，确认没有404错误
4. 确认页面样式正常显示

## 🔍 路径映射验证

修复后的路径映射应该是：

| 外部请求 | 内部转发 | 说明 |
|---------|---------|------|
| `https://jwd.vooice.tech/perception/demo.html` | `http://localhost:8000/demo.html` | ✅ 正确 |
| `https://jwd.vooice.tech/perception/static/demo.css` | `http://localhost:8000/static/demo.css` | ✅ 正确 |
| `https://jwd.vooice.tech/perception/static/demo.js` | `http://localhost:8000/static/demo.js` | ✅ 正确 |

## 🚨 故障排除

### 如果仍然出现404错误：

1. **检查本地服务器状态**
   ```bash
   curl http://localhost:8000/demo.html
   curl http://localhost:8000/static/demo.css
   curl http://localhost:8000/static/demo.js
   ```

2. **检查nginx错误日志**
   ```bash
   sudo tail -f /opt/homebrew/var/log/nginx/error.log
   ```

3. **检查nginx访问日志**
   ```bash
   sudo tail -f /opt/homebrew/var/log/nginx/access.log
   ```

4. **验证rewrite规则**
   在nginx配置中临时添加调试信息：
   ```nginx
   location /perception/ {
       rewrite_log on;
       error_log /opt/homebrew/var/log/nginx/rewrite.log notice;
       rewrite ^/perception/(.*)$ /$1 break;
       # ... 其他配置
   }
   ```

## 🎯 预期结果

修复完成后：
- ✅ `https://jwd.vooice.tech/perception/demo.html` 正常访问
- ✅ CSS和JS文件正常加载，无404错误
- ✅ 页面样式和功能完全正常
- ✅ 聊天和图片上传功能正常工作

这个修复方案解决了路径重写问题，确保静态资源能够正确加载！🚀
