# OpenAI API Server 重构建议

## 🎯 当前问题分析

### 1. 代码重复问题
当前的`create_chat_completion`函数中，流式和非流式输出存在大量重复代码：

- **重复的感知API调用逻辑**
- **重复的错误处理**
- **重复的响应格式化**
- **重复的对象名称提取**

### 2. 函数调用支持不完整
- 当前只支持`check_image`一个函数
- 没有实现OpenAI标准的Function Calling机制
- 缺少智能函数选择逻辑

## 🏗️ 重构方案

### 方案1: 抽象化重构（推荐）

#### 1.1 创建处理器类

```python
class PerceptionHandler:
    """感知API处理器 - 统一处理流式和非流式请求"""
    
    def __init__(self, logger):
        self.logger = logger
        self.function_map = {
            "check_image": self._call_check_image,
            "split_image": self._call_split_image,
            "props_describe": self._call_props_describe,
            "angle_prediction": self._call_angle_prediction,
            "key_point_prediction": self._call_key_point_prediction,
            "grab_point_prediction": self._call_grab_point_prediction,
            "full_perception": self._call_full_perception
        }
    
    def determine_function(self, text_content: str, functions: list = None, 
                          function_call = None) -> tuple:
        """智能确定要调用的函数"""
        # 如果明确指定了function_call，使用指定的函数
        if function_call:
            if isinstance(function_call, dict):
                return function_call.get("name", "check_image"), {}
            return function_call, {}
        
        # 根据文本内容智能选择
        text_lower = text_content.lower()
        
        if any(word in text_lower for word in ["split", "segment", "separate"]):
            return "split_image", {}
        elif any(word in text_lower for word in ["describe", "property", "detail"]):
            return "props_describe", {"questions": ["Describe this object in detail"]}
        elif any(word in text_lower for word in ["angle", "rotation", "orientation"]):
            return "angle_prediction", {}
        elif any(word in text_lower for word in ["keypoint", "key point", "landmark"]):
            return "key_point_prediction", {}
        elif any(word in text_lower for word in ["grab", "grasp", "pick"]):
            return "grab_point_prediction", {}
        elif any(word in text_lower for word in ["full", "complete", "comprehensive"]):
            return "full_perception", {"questions": ["Provide comprehensive analysis"]}
        else:
            return "check_image", {}
    
    async def process_request(self, perception: Perception, image_url: str, 
                            text_content: str, function_name: str, 
                            function_params: dict) -> str:
        """统一的请求处理逻辑"""
        try:
            # 提取对象名称
            object_names = extract_object_names_from_text(text_content)
            if not object_names:
                object_names = ["car", "person", "object"]
            
            # 调用对应的感知函数
            if function_name in self.function_map:
                result = await self.function_map[function_name](
                    perception, image_url, object_names, function_params
                )
            else:
                raise ValueError(f"Unknown function: {function_name}")
            
            # 格式化结果
            content_parts = parse_perception_result(result, text_content)
            return "".join(content_parts)
            
        except Exception as e:
            self.logger.error(f"Perception processing error: {e}")
            raise
    
    async def _call_check_image(self, perception, image_url, object_names, params):
        """调用check_image函数"""
        return perception.check_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    async def _call_split_image(self, perception, image_url, object_names, params):
        """调用split_image函数"""
        return perception.split_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
    
    # ... 其他函数的实现
```

#### 1.2 重构主函数

```python
# 创建全局处理器实例
perception_handler = PerceptionHandler(logger)

@app.post("/v1/chat/completions")
async def create_chat_completion(
    request: ChatCompletionRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """Create a chat completion with function calling support"""
    try:
        # 验证和初始化（保持不变）
        token = credentials.credentials
        creds = token_manager.get_credentials(token)
        if not creds:
            raise HTTPException(status_code=401, detail="Invalid API key")

        app_id, app_secret = creds
        os.environ['QJ_APP_ID'] = app_id
        os.environ['QJ_APP_SECRET'] = app_secret

        # 提取消息内容
        messages = [msg.model_dump() for msg in request.messages]
        image_data, text_content = extract_image_and_text(messages)
        image_url = process_image_data(image_data)

        # 创建感知API实例
        perception = Perception()

        # 确定要调用的函数
        function_name, function_params = perception_handler.determine_function(
            text_content, 
            getattr(request, 'functions', None),
            getattr(request, 'function_call', None)
        )

        # 根据stream参数选择处理方式
        if request.stream:
            return await create_streaming_response(
                request, perception, image_url, text_content, 
                function_name, function_params
            )
        else:
            return await create_non_streaming_response(
                request, perception, image_url, text_content,
                function_name, function_params
            )

    except Exception as e:
        logger.error(f"Chat completion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

#### 1.3 抽象响应生成

```python
async def create_streaming_response(request, perception, image_url, text_content,
                                  function_name, function_params):
    """创建流式响应"""
    def generate_stream():
        try:
            completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
            current_time = int(time.time())
            
            # 发送开始chunk
            yield create_chunk_data(completion_id, request.model, current_time, 
                                  {"role": "assistant", "content": ""})
            
            # 发送状态信息
            status_messages = [
                "🔍 正在分析图片...",
                f"\n📊 调用{function_name}功能...",
                "\n🤖 处理感知结果..."
            ]
            
            for status_msg in status_messages:
                yield create_chunk_data(completion_id, request.model, current_time,
                                      {"content": status_msg})
                time.sleep(0.5)
            
            # 处理感知请求
            response_content = asyncio.run(
                perception_handler.process_request(
                    perception, image_url, text_content, 
                    function_name, function_params
                )
            )
            
            # 流式发送内容
            yield from stream_content(completion_id, request.model, current_time, 
                                    response_content)
            
            # 发送结束chunk
            yield create_chunk_data(completion_id, request.model, current_time,
                                  {}, finish_reason="stop")
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            logger.error(f"Streaming error: {e}")
            yield create_error_chunk(request.model, str(e))
            yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive", 
            "Content-Type": "text/event-stream"
        }
    )

def create_chunk_data(completion_id, model, created_time, delta, finish_reason=None):
    """创建chunk数据"""
    chunk = {
        "id": completion_id,
        "object": "chat.completion.chunk",
        "created": created_time,
        "model": model,
        "choices": [{
            "index": 0,
            "delta": delta,
            "finish_reason": finish_reason
        }]
    }
    return f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

def stream_content(completion_id, model, created_time, content):
    """流式发送内容"""
    words = content.split()
    chunk_size = 2
    
    for i in range(0, len(words), chunk_size):
        chunk_words = words[i:i + chunk_size]
        chunk_content = " ".join(chunk_words)
        if i == 0:
            chunk_content = "\n" + chunk_content
        else:
            chunk_content = " " + chunk_content
        
        yield create_chunk_data(completion_id, model, created_time,
                              {"content": chunk_content})
        time.sleep(0.15)
```

### 方案2: Function Calling 标准实现

#### 2.1 更新请求模型

```python
class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[Message]
    functions: Optional[List[Dict]] = None  # 新增
    function_call: Optional[Union[str, Dict]] = None  # 新增
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = None
    stream: bool = False
    # ... 其他字段
```

#### 2.2 实现Function Calling响应

```python
def create_function_call_response(completion_id, model, function_name, arguments):
    """创建函数调用响应"""
    return {
        "id": completion_id,
        "object": "chat.completion",
        "created": int(time.time()),
        "model": model,
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": None,
                "function_call": {
                    "name": function_name,
                    "arguments": json.dumps(arguments)
                }
            },
            "finish_reason": "function_call"
        }]
    }
```

## 🎯 推荐实施步骤

### 阶段1: 代码抽象（1-2天）
1. 创建`PerceptionHandler`类
2. 抽象流式和非流式响应生成
3. 统一错误处理逻辑

### 阶段2: Function Calling支持（2-3天）
1. 更新请求模型支持functions参数
2. 实现智能函数选择逻辑
3. 添加所有7个感知函数的支持

### 阶段3: 测试和优化（1-2天）
1. 编写单元测试
2. 性能优化
3. 文档更新

## 🎉 预期收益

1. **代码质量提升**
   - 减少重复代码约60%
   - 提高可维护性
   - 更好的错误处理

2. **功能完整性**
   - 支持所有7个感知函数
   - 符合OpenAI Function Calling标准
   - 智能函数选择

3. **开发效率**
   - 新增函数更容易
   - 统一的处理逻辑
   - 更好的测试覆盖

这个重构方案既解决了代码重复问题，又实现了完整的Function Calling支持，是一个平衡的解决方案！🚀
