# OpenAI Function Calling 使用示例

## 🎯 概述

本文档展示如何使用QJ Robots感知API的OpenAI Function Calling功能。

## 📋 支持的函数调用模式

### 1. function_call="auto" (智能模式)
系统自动判断是否需要调用函数，并选择最合适的函数。

### 2. function_call={"name": "function_name"} (指定模式)
强制调用指定的函数。

### 3. function_call="none" (禁用模式)
禁用函数调用，直接生成文本回复。

## 🔧 API调用示例

### 示例1: 智能模式 - 自动选择函数

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "qj-perception-v1",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "请分析这张图片中的汽车"},
          {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
        ]
      }
    ],
    "functions": [
      {
        "name": "check_image",
        "description": "检测和定位图像中的对象",
        "parameters": {
          "type": "object",
          "properties": {
            "image_url": {"type": "string", "description": "图像URL"},
            "object_names": {"type": "array", "items": {"type": "string"}}
          },
          "required": ["image_url", "object_names"]
        }
      }
    ],
    "function_call": "auto"
  }'
```

**响应 (第一步 - 函数调用)**:
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": 1677652288,
  "model": "qj-perception-v1",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": null,
      "function_call": {
        "name": "check_image",
        "arguments": "{\"image_url\":\"https://example.com/car.jpg\",\"object_names\":[\"car\"]}"
      }
    },
    "finish_reason": "function_call"
  }]
}
```

**第二步 - 发送函数执行结果**:
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "qj-perception-v1",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "请分析这张图片中的汽车"},
          {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
        ]
      },
      {
        "role": "assistant",
        "content": null,
        "function_call": {
          "name": "check_image",
          "arguments": "{\"image_url\":\"https://example.com/car.jpg\",\"object_names\":[\"car\"]}"
        }
      },
      {
        "role": "function",
        "name": "check_image",
        "content": "{\"taskStatus\":\"DONE\",\"taskResult\":{\"labels\":[\"car\"],\"scores\":[0.95],\"boxes\":[[100,200,300,400]]}}"
      }
    ]
  }'
```

**响应 (第二步 - 最终结果)**:
```json
{
  "id": "chatcmpl-124",
  "object": "chat.completion", 
  "created": 1677652289,
  "model": "qj-perception-v1",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "🔍 **图像对象检测结果**\n\n✅ 检测完成！\n\n**检测到的对象：**\n• car：1个\n\n📊 平均检测置信度：95.0%"
    },
    "finish_reason": "stop"
  }]
}
```

### 示例2: 指定模式 - 强制调用特定函数

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "qj-perception-v1",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "这是一张图片"},
          {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
        ]
      }
    ],
    "functions": [
      {
        "name": "split_image",
        "description": "对图像进行语义分割",
        "parameters": {
          "type": "object",
          "properties": {
            "image_url": {"type": "string"},
            "object_names": {"type": "array", "items": {"type": "string"}}
          },
          "required": ["image_url", "object_names"]
        }
      }
    ],
    "function_call": {"name": "split_image"}
  }'
```

### 示例3: 禁用模式 - 纯文本对话

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "qj-perception-v1",
    "messages": [
      {
        "role": "user",
        "content": "你好，请介绍一下你的功能"
      }
    ],
    "function_call": "none"
  }'
```

## 🎯 智能函数选择示例

系统会根据用户输入的关键词自动选择最合适的函数：

| 用户输入 | 自动选择的函数 | 匹配关键词 |
|---------|---------------|-----------|
| "请分割这张图片" | `split_image` | "分割" |
| "描述一下这个对象" | `props_describe` | "描述" |
| "这个物体的角度是多少？" | `angle_prediction` | "角度" |
| "找到关键点" | `key_point_prediction` | "关键点" |
| "机器人应该从哪里抓取？" | `grab_point_prediction` | "抓取" |
| "全面分析这张图片" | `full_perception` | "全面", "分析" |
| "检测图片中的汽车" | `check_image` | "检测" |

## 🔧 Python客户端示例

### 使用OpenAI官方客户端

```python
import openai

# 配置客户端
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "your-token"

# 智能模式调用
response = openai.ChatCompletion.create(
    model="qj-perception-v1",
    messages=[
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "请分析这张图片中的汽车"},
                {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
            ]
        }
    ],
    functions=[
        {
            "name": "check_image",
            "description": "检测和定位图像中的对象",
            "parameters": {
                "type": "object",
                "properties": {
                    "image_url": {"type": "string"},
                    "object_names": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["image_url", "object_names"]
            }
        }
    ],
    function_call="auto"
)

print(response)
```

### 处理函数调用响应

```python
def handle_function_call(response):
    """处理函数调用响应"""
    choice = response.choices[0]
    
    if choice.finish_reason == "function_call":
        # 需要执行函数调用
        function_call = choice.message.function_call
        function_name = function_call.name
        arguments = json.loads(function_call.arguments)
        
        print(f"需要调用函数: {function_name}")
        print(f"参数: {arguments}")
        
        # 这里应该执行实际的函数调用
        # 然后发送结果给API
        
        return True
    else:
        # 直接的文本响应
        print(f"回复: {choice.message.content}")
        return False
```

## 📊 所有支持的函数

### 1. check_image - 对象检测
```json
{
  "name": "check_image",
  "description": "检测和定位图像中的对象，返回对象位置、类别和置信度",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string", "description": "要分析的图像URL"},
      "object_names": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 2. split_image - 图像分割
```json
{
  "name": "split_image",
  "description": "对图像进行语义分割，将不同对象分离出来",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string"},
      "object_names": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 3. props_describe - 属性描述
```json
{
  "name": "props_describe",
  "description": "详细描述图像中对象的属性特征",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string"},
      "object_names": {"type": "array", "items": {"type": "string"}},
      "questions": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 4. angle_prediction - 角度预测
```json
{
  "name": "angle_prediction",
  "description": "预测图像中对象的角度和方向信息",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string"},
      "object_names": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 5. key_point_prediction - 关键点预测
```json
{
  "name": "key_point_prediction",
  "description": "预测图像中对象的关键点位置",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string"},
      "object_names": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 6. grab_point_prediction - 抓取点预测
```json
{
  "name": "grab_point_prediction",
  "description": "为机器人预测最佳的对象抓取点",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string"},
      "object_names": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

### 7. full_perception - 综合感知
```json
{
  "name": "full_perception",
  "description": "对图像进行全面的感知分析，包括检测、分割、描述等",
  "parameters": {
    "type": "object",
    "properties": {
      "image_url": {"type": "string"},
      "object_names": {"type": "array", "items": {"type": "string"}},
      "questions": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["image_url", "object_names"]
  }
}
```

## 🎉 总结

QJ Robots感知API现在完全支持OpenAI Function Calling标准，提供：

1. **✅ 标准兼容**: 完全符合OpenAI Function Calling规范
2. **✅ 智能选择**: 自动判断何时调用哪个函数
3. **✅ 多种模式**: 支持auto、指定和禁用三种模式
4. **✅ 丰富功能**: 7个专业的感知函数
5. **✅ 易于集成**: 与现有OpenAI工具无缝集成

开始使用Function Calling来构建更智能的视觉AI应用吧！🚀
