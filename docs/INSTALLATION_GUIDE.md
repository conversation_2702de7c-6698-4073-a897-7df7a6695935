# QJ Robots OpenAI API - Installation Guide

## 📦 Dependencies Overview

The QJ Robots OpenAI API has different dependency requirements based on your usage:

### Core Dependencies (Always Required)
- `requests>=2.26.0` - HTTP client for API calls
- `python-dotenv>=0.19.0` - Environment variable management

### OpenAI API Dependencies (For Server Functionality)
- `fastapi>=0.68.0` - Web framework for REST API
- `uvicorn[standard]>=0.15.0` - ASGI server
- `pydantic>=1.8.0` - Data validation and serialization

### Optional Dependencies
- `openai>=0.27.0` - OpenAI client library (for examples and testing)

## 🚀 Installation Methods

### Method 1: Automatic Installation (Recommended)

Use the provided installation script:

```bash
python install_dependencies.py
```

This script will:
- Check your Python version compatibility
- Install all required dependencies
- Verify the installation
- Create development requirements file

### Method 2: Using Requirements Files

#### For Basic Usage (Core SDK Only)
```bash
pip install -r requirements.txt
```

#### For OpenAI API Server Functionality
```bash
pip install -r requirements-openai.txt
```

### Method 3: Using Setup.py with Extras

#### Install with OpenAI API support
```bash
pip install -e .[openai]
```

#### Install with development dependencies
```bash
pip install -e .[dev]
```

### Method 4: Manual Installation

#### Core dependencies only:
```bash
pip install requests>=2.26.0 python-dotenv>=0.19.0
```

#### With OpenAI API support:
```bash
pip install requests>=2.26.0 python-dotenv>=0.19.0 fastapi>=0.68.0 uvicorn[standard]>=0.15.0 pydantic>=1.8.0
```

#### With OpenAI client for examples:
```bash
pip install openai>=0.27.0
```

## 🔧 Verification

### Quick Verification
```bash
python install_dependencies.py --verify-only
```

### Manual Verification
```python
# Test core functionality
from py_qj_robots import Perception
print("✅ Core SDK works")

# Test OpenAI API functionality
from py_qj_robots import OpenAIPerceptionAPI
print("✅ OpenAI API works")

# Test server dependencies
import fastapi
import uvicorn
import pydantic
print("✅ Server dependencies available")
```

### Run Test Suite
```bash
python test_openai_api.py
python test_token_auth.py
```

## 🐍 Python Version Requirements

- **Minimum**: Python 3.7+
- **Recommended**: Python 3.8+
- **FastAPI requires**: Python 3.7+

Check your Python version:
```bash
python --version
```

## 🔄 Virtual Environment (Recommended)

Create and activate a virtual environment:

```bash
# Create virtual environment
python -m venv venv

# Activate (Linux/Mac)
source venv/bin/activate

# Activate (Windows)
venv\Scripts\activate

# Install dependencies
pip install -r requirements-openai.txt
```

## 📋 Requirements Files Explained

### `requirements.txt` (Basic)
Contains only core dependencies needed for the basic Perception SDK.

### `requirements-openai.txt` (Complete)
Contains all dependencies needed for OpenAI API compatibility, including FastAPI server.

### `requirements-dev.txt` (Development)
Auto-generated file with all dependencies including testing and development tools.

## 🚨 Common Issues and Solutions

### Issue: FastAPI not found
```bash
# Solution
pip install fastapi uvicorn pydantic
```

### Issue: Python version too old
```bash
# Check version
python --version

# Upgrade Python or use pyenv/conda
pyenv install 3.9.0
pyenv global 3.9.0
```

### Issue: uvicorn installation fails
```bash
# Try without standard extras
pip install uvicorn

# Or install with specific extras
pip install "uvicorn[standard]"
```

### Issue: Pydantic compatibility
```bash
# Install specific version
pip install "pydantic>=1.8.0,<2.0.0"
```

## 🎯 Usage-Based Installation

### Just want to use the Perception SDK?
```bash
pip install -r requirements.txt
```

### Want to run the OpenAI-compatible server?
```bash
pip install -r requirements-openai.txt
```

### Want to develop and contribute?
```bash
pip install -e .[dev]
```

### Want to run all examples?
```bash
pip install -r requirements-openai.txt
pip install openai  # For OpenAI client examples
```

## 🧪 Testing Your Installation

### Test Core SDK
```bash
python -c "from py_qj_robots import Perception; print('✅ Core SDK OK')"
```

### Test OpenAI API
```bash
python -c "from py_qj_robots import OpenAIPerceptionAPI; print('✅ OpenAI API OK')"
```

### Test Server Dependencies
```bash
python -c "import fastapi, uvicorn, pydantic; print('✅ Server deps OK')"
```

### Run Full Test Suite
```bash
python test_openai_api.py
```

## 📚 Next Steps After Installation

1. **Configure Tokens**: Edit `token_mapping.json` with your credentials
2. **Start Server**: `python openai_api_server.py`
3. **Test API**: `python openai_client_example.py`
4. **Read Documentation**: Check `OPENAI_API_README.md`

## 🆘 Getting Help

If you encounter issues:

1. **Check Python version**: Must be 3.7+
2. **Update pip**: `pip install --upgrade pip`
3. **Use virtual environment**: Avoid conflicts
4. **Check error messages**: Often indicate missing dependencies
5. **Run verification**: `python install_dependencies.py --verify-only`

## 🔄 Updating Dependencies

To update all dependencies:
```bash
pip install --upgrade -r requirements-openai.txt
```

To update specific packages:
```bash
pip install --upgrade fastapi uvicorn pydantic
```

---

**Need help?** Check the error messages carefully - they usually indicate which package is missing or incompatible.
