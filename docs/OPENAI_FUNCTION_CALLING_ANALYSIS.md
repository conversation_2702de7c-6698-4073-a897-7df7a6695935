# OpenAI Function Calling 架构分析

## 🎯 核心问题

您的感知模型提供了7个API，需要决定架构方案：
1. **方案A**: 7个独立模型 (`qj-perception-check`, `qj-perception-split`, etc.)
2. **方案B**: 1个模型 + 7个函数 (`qj-perception-v1` with functions)

## 📋 OpenAI Function Calling 标准

### 1. 标准接口支持

OpenAI Chat Completions API **完全支持** Function Calling：

```json
{
  "model": "gpt-4",
  "messages": [...],
  "functions": [
    {
      "name": "check_image",
      "description": "Check and detect objects in an image",
      "parameters": {
        "type": "object",
        "properties": {
          "image_url": {"type": "string"},
          "object_names": {"type": "array", "items": {"type": "string"}}
        },
        "required": ["image_url", "object_names"]
      }
    }
  ],
  "function_call": "auto" // 或 {"name": "check_image"}
}
```

### 2. Function Calling 工作流程

```
1. 用户发送消息 + 可用函数列表
   ↓
2. 模型决定是否需要调用函数
   ↓
3. 如果需要，返回function_call响应
   ↓
4. 客户端执行函数并发送结果
   ↓
5. 模型基于函数结果生成最终回复
```

## 🏗️ 架构方案对比

### 方案A: 多模型架构

#### 优点
- ✅ **语义清晰**: 每个模型名称直接表达功能
- ✅ **独立版本**: 每个功能可以独立更新版本
- ✅ **简单调用**: 直接指定模型即可
- ✅ **负载分离**: 可以为不同功能分配不同资源

#### 缺点
- ❌ **管理复杂**: 需要维护7个模型定义
- ❌ **不符合标准**: 偏离OpenAI的Function Calling模式
- ❌ **客户端复杂**: 客户端需要知道所有模型名称

#### 实现示例
```python
models = [
    "qj-perception-check-v1",      # 对象检测
    "qj-perception-split-v1",      # 图像分割
    "qj-perception-props-v1",      # 属性描述
    "qj-perception-angle-v1",      # 角度预测
    "qj-perception-keypoint-v1",   # 关键点预测
    "qj-perception-grab-v1",       # 抓取点预测
    "qj-perception-full-v1"        # 全面感知
]
```

### 方案B: 单模型 + 函数架构 (推荐)

#### 优点
- ✅ **符合标准**: 完全遵循OpenAI Function Calling规范
- ✅ **智能选择**: 模型可以根据用户意图自动选择函数
- ✅ **组合调用**: 可以在一次对话中调用多个函数
- ✅ **易于扩展**: 添加新功能只需添加函数定义
- ✅ **客户端友好**: 标准OpenAI客户端库直接支持

#### 缺点
- ❌ **实现复杂**: 需要实现函数调用逻辑
- ❌ **单点负载**: 所有请求都到同一个模型

#### 实现示例
```python
model = "qj-perception-v1"
functions = [
    {
        "name": "check_image",
        "description": "Check and detect objects in an image",
        "parameters": {...}
    },
    {
        "name": "split_image", 
        "description": "Segment and split objects in an image",
        "parameters": {...}
    },
    # ... 其他5个函数
]
```

## 🎯 推荐方案：单模型 + 函数架构

### 理由分析

1. **标准兼容性** ⭐⭐⭐⭐⭐
   - 完全符合OpenAI标准
   - 现有工具和库直接支持
   - 未来兼容性好

2. **用户体验** ⭐⭐⭐⭐⭐
   - 用户只需要知道一个模型名
   - 可以用自然语言描述需求
   - 模型智能选择合适的功能

3. **开发效率** ⭐⭐⭐⭐
   - 标准化的实现模式
   - 丰富的参考资料
   - 社区支持好

4. **维护成本** ⭐⭐⭐⭐
   - 统一的版本管理
   - 集中的配置管理
   - 简化的部署流程

## 🔧 实现建议

### 1. 函数定义优化

```python
def _setup_functions(self):
    """Setup available perception functions with enhanced descriptions"""
    self.functions = {
        "check_image": PerceptionFunction(
            name="check_image",
            description="Detect and locate objects in an image. Use this for basic object detection tasks.",
            parameters={
                "type": "object",
                "properties": {
                    "image_type": {
                        "type": "string",
                        "enum": ["2D", "3D"],
                        "description": "Type of image to process",
                        "default": "2D"
                    },
                    "image_url": {
                        "type": "string",
                        "description": "URL of the image to analyze"
                    },
                    "object_names": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Names of objects to detect (e.g., ['car', 'person', 'tree'])"
                    }
                },
                "required": ["image_url", "object_names"]
            }
        ),
        # ... 其他函数定义
    }
```

### 2. 智能函数选择

```python
def determine_best_function(self, user_message: str) -> str:
    """根据用户消息智能选择最合适的函数"""
    message_lower = user_message.lower()
    
    # 关键词映射
    function_keywords = {
        "check_image": ["detect", "find", "locate", "identify", "check"],
        "split_image": ["split", "segment", "separate", "divide", "cut"],
        "props_describe": ["describe", "properties", "details", "characteristics"],
        "angle_prediction": ["angle", "rotation", "orientation", "direction"],
        "key_point_prediction": ["keypoint", "landmark", "key point", "joints"],
        "grab_point_prediction": ["grab", "grasp", "pick", "hold", "manipulation"],
        "full_perception": ["full", "complete", "comprehensive", "everything", "all"]
    }
    
    # 计算匹配分数
    scores = {}
    for func_name, keywords in function_keywords.items():
        score = sum(1 for keyword in keywords if keyword in message_lower)
        if score > 0:
            scores[func_name] = score
    
    # 返回得分最高的函数，默认为check_image
    return max(scores.items(), key=lambda x: x[1])[0] if scores else "check_image"
```

### 3. 函数调用响应格式

```python
def create_function_call_response(self, function_name: str, arguments: dict) -> dict:
    """创建函数调用响应"""
    return {
        "id": f"chatcmpl-{uuid.uuid4().hex[:29]}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": "qj-perception-v1",
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": None,
                "function_call": {
                    "name": function_name,
                    "arguments": json.dumps(arguments)
                }
            },
            "finish_reason": "function_call"
        }]
    }
```

## 🚀 迁移路径

### 阶段1: 保持向后兼容
```python
# 同时支持两种方式
@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    # 如果指定了特定的感知模型名，直接调用对应功能
    if request.model.startswith("qj-perception-"):
        function_name = request.model.replace("qj-perception-", "").replace("-v1", "")
        return await call_specific_function(function_name, request)
    
    # 否则使用函数调用模式
    return await call_with_functions(request)
```

### 阶段2: 逐步迁移
- 更新文档，推荐使用函数调用方式
- 在响应中添加deprecation警告
- 提供迁移指南

### 阶段3: 完全切换
- 移除多模型支持
- 统一使用 `qj-perception-v1` + functions

## 🎉 总结

**强烈推荐使用方案B（单模型 + 函数架构）**，因为：

1. ✅ **标准化**: 完全符合OpenAI Function Calling标准
2. ✅ **智能化**: 可以根据用户意图自动选择功能
3. ✅ **可扩展**: 易于添加新功能和组合调用
4. ✅ **兼容性**: 与现有工具和库完美集成
5. ✅ **用户友好**: 简化客户端使用复杂度

这种架构不仅技术上更优雅，也更符合AI助手的发展趋势！🚀
