# Nginx配置指南 - 感知API测试页面

## 🎯 配置目标

通过 `https://jwd.vooice.tech/perception/demo.html` 访问感知API测试页面，确保：
1. ✅ 静态资源正确加载（CSS、JS文件）
2. ✅ API接口正确转发
3. ✅ 上传参数接口正确转发
4. ✅ 避免404错误

## 🔧 Nginx配置修改

### 1. 新增感知API测试页面配置

在nginx.conf的HTTPS server块中添加了 `/perception/` 路径配置：

```nginx
# 感知API测试页面配置
location /perception/ {
    # CORS 配置
    set $cors_origin "";
    if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
        set $cors_origin $http_origin;
    }

    # 预检请求处理
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' $cors_origin always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
        add_header 'Access-Control-Max-Age' 86400 always;
        add_header 'Content-Length' 0;
        return 204;
    }

    # 常规请求处理
    add_header 'Access-Control-Allow-Origin' $cors_origin always;
    add_header 'Access-Control-Allow-Credentials' 'true' always;

    # 转发到本地感知API服务器 (端口 8000)
    proxy_pass http://localhost:8000/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;

    # 超时设置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 300s;
}
```

### 2. 更新open-api配置

修改了 `/open-api/` 的CORS配置，允许来自 `*.vooice.tech` 域名的请求：

```nginx
location /open-api/ {
    set $cors_origin "";
    if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
        set $cors_origin $http_origin;
    }
    # ... 其他配置
}
```

## 🌐 URL路径映射

### 1. 测试页面访问
- **外部URL**: `https://jwd.vooice.tech/perception/demo.html`
- **内部转发**: `http://localhost:8000/demo.html`
- **说明**: `/perception/` 路径被代理到本地8000端口的感知API服务器

### 2. 静态资源访问
- **CSS文件**: `https://jwd.vooice.tech/perception/static/demo.css`
- **JS文件**: `https://jwd.vooice.tech/perception/static/demo.js`
- **内部转发**: `http://localhost:8000/static/demo.css` 和 `http://localhost:8000/static/demo.js`

### 3. API接口访问
- **聊天API**: `https://jwd.vooice.tech/v1/chat/completions`
- **内部转发**: `http://localhost:8000/v1/chat/completions`

### 4. 上传参数接口
- **外部URL**: `https://jwd.vooice.tech/open-api/open-apis/base/upload/params`
- **内部转发**: `http://localhost:9999/open-apis/base/upload/params`

## 📝 JavaScript自适应配置

### 1. Base URL自动检测

```javascript
function getBaseUrl() {
    const inputValue = $('#baseUrl').val();
    if (inputValue) {
        return inputValue;
    }
    
    // 根据当前域名自动判断API地址
    if (window.location.hostname === 'jwd.vooice.tech') {
        return 'https://jwd.vooice.tech';
    } else {
        return 'http://localhost:8000';
    }
}
```

### 2. 上传参数接口自动检测

```javascript
async function getUploadParams() {
    // 根据当前域名自动判断上传参数接口地址
    let uploadParamsUrl;
    if (window.location.hostname === 'jwd.vooice.tech') {
        uploadParamsUrl = 'https://jwd.vooice.tech/open-api/open-apis/base/upload/params';
    } else {
        uploadParamsUrl = 'http://localhost:9999/open-apis/base/upload/params';
    }
    
    const response = await fetch(uploadParamsUrl, {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    });
    // ...
}
```

### 3. 页面初始化时自动设置

```javascript
function initializeApp() {
    // ... 其他初始化代码 ...
    
    // 根据当前域名自动设置Base URL
    if (window.location.hostname === 'jwd.vooice.tech') {
        $('#baseUrl').val('https://jwd.vooice.tech');
    } else {
        $('#baseUrl').val('http://localhost:8000');
    }
}
```

## 🔍 路径解析详情

### 1. 请求流程示例

当用户访问 `https://jwd.vooice.tech/perception/demo.html` 时：

```
1. 用户请求: https://jwd.vooice.tech/perception/demo.html
   ↓
2. Nginx匹配: location /perception/
   ↓
3. 代理转发: http://localhost:8000/demo.html
   ↓
4. 本地服务器: 返回demo.html页面
   ↓
5. 浏览器加载: 请求CSS和JS资源
   ↓
6. 资源请求: https://jwd.vooice.tech/perception/static/demo.css
   ↓
7. Nginx转发: http://localhost:8000/static/demo.css
   ↓
8. 返回资源: CSS文件内容
```

### 2. API调用流程

当页面发送聊天请求时：

```
1. JS调用: fetch('https://jwd.vooice.tech/v1/chat/completions')
   ↓
2. Nginx匹配: location /v1/
   ↓
3. 代理转发: http://localhost:8000/v1/chat/completions
   ↓
4. 感知API: 处理请求并返回结果
```

### 3. 上传参数获取流程

当需要上传图片时：

```
1. JS调用: fetch('https://jwd.vooice.tech/open-api/open-apis/base/upload/params')
   ↓
2. Nginx匹配: location /open-api/
   ↓
3. 代理转发: http://localhost:9999/open-apis/base/upload/params
   ↓
4. 上传服务: 返回腾讯云COS上传参数
```

## 🛠️ 部署检查清单

### 1. Nginx配置检查
- [ ] 确认SSL证书路径正确
- [ ] 确认server_name包含 `jwd.vooice.tech`
- [ ] 确认 `/perception/` location配置已添加
- [ ] 确认 `/open-api/` CORS配置已更新

### 2. 服务状态检查
- [ ] 端口8000的感知API服务器正在运行
- [ ] 端口9999的上传参数服务正在运行
- [ ] Nginx服务正在运行并监听443端口

### 3. 功能测试
- [ ] 访问 `https://jwd.vooice.tech/perception/demo.html` 页面正常加载
- [ ] CSS和JS文件正常加载，无404错误
- [ ] 聊天功能正常工作
- [ ] 图片上传功能正常工作

## 🎉 预期效果

配置完成后，用户可以：

1. ✅ 通过 `https://jwd.vooice.tech/perception/demo.html` 访问测试页面
2. ✅ 页面自动检测环境并设置正确的API地址
3. ✅ 所有静态资源正确加载，无404错误
4. ✅ 聊天功能正常工作
5. ✅ 图片上传功能正常工作
6. ✅ 完整的HTTPS安全访问

这样的配置确保了在生产环境中的完整功能性和安全性！🚀
