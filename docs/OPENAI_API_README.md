# QJ Robots OpenAI-Compatible Perception API

This module provides an OpenAI-style interface for QJ Robots Perception services, making it easy to integrate perception capabilities into existing OpenAI-based applications and workflows.

## Features

- **OpenAI-Compatible Interface**: Use standard OpenAI client libraries and patterns
- **Streaming Support**: Real-time streaming responses like OpenAI's API
- **Function Calling**: Structured function calls for different perception tasks
- **Async Support**: Asynchronous processing for high-performance applications
- **REST API Server**: FastAPI-based server with OpenAI-compatible endpoints
- **Error Handling**: Proper error responses matching OpenAI's format

## Available Perception Functions

1. **check_image**: Object detection and recognition
2. **split_image**: Image segmentation and object splitting
3. **props_describe**: Detailed property descriptions of objects
4. **angle_prediction**: Angle prediction for objects
5. **key_point_prediction**: Key point detection
6. **grab_point_prediction**: Grasp point prediction for robotics
7. **full_perception**: Comprehensive analysis with all functions

## Installation

```bash
# Install the package
pip install py-qj-robots

# For server functionality, install additional dependencies
pip install fastapi uvicorn
```

## Quick Start

### 1. Direct API Usage

```python
from py_qj_robots import OpenAIPerceptionAPI
import json

# Initialize the API
api = OpenAIPerceptionAPI()

# Create a perception request
messages = [
    {
        "role": "user",
        "content": json.dumps({
            "function": "check_image",
            "arguments": {
                "image_type": "2D",
                "image_url": "https://example.com/image.jpg",
                "object_names": ["apple", "banana"]
            }
        })
    }
]

# Get response
response = api.create_chat_completion(messages=messages)
print(response.choices[0].message.content)
```

### 2. Streaming Response

```python
# Enable streaming
stream = api.create_chat_completion(messages=messages, stream=True)

for chunk in stream:
    if chunk.choices and chunk.choices[0].get("delta", {}).get("content"):
        print(chunk.choices[0]["delta"]["content"], end="", flush=True)
```

### 3. Async Processing

```python
# Initialize with async mode
api = OpenAIPerceptionAPI(enable_async_mode=True)

def callback(result, error):
    if error:
        print(f"Error: {error}")
    else:
        print(f"Result: {result.choices[0].message.content}")

# Submit async request
task_id = api.create_chat_completion_async(
    messages=messages,
    callback=callback
)
```

## REST API Server

### Starting the Server

```bash
python openai-server/openai_api_server.py
```

The server will be available at `http://localhost:8000` with the following endpoints:

- `POST /v1/chat/completions` - Create chat completions (OpenAI compatible)
- `GET /v1/models` - List available models
- `GET /v1/functions` - List available perception functions
- `GET /docs` - API documentation

### Using with OpenAI Client Libraries

```python
import openai
import json

# Configure to use local server
openai.api_base = "http://localhost:8000/v1"
openai.api_key = "dummy-key"

# Make request
response = openai.ChatCompletion.create(
    model="qj-perception-v1",
    messages=[
        {
            "role": "user",
            "content": json.dumps({
                "function": "split_image",
                "arguments": {
                    "image_type": "2D",
                    "image_url": "https://example.com/image.jpg",
                    "object_names": ["cup", "plate"]
                }
            })
        }
    ]
)

print(response.choices[0].message.content)
```

### Using with HTTP Requests

```python
import requests
import json

response = requests.post(
    "http://localhost:8000/v1/chat/completions",
    json={
        "model": "qj-perception-v1",
        "messages": [
            {
                "role": "user",
                "content": json.dumps({
                    "function": "props_describe",
                    "arguments": {
                        "image_type": "2D",
                        "image_url": "https://example.com/fruit.jpg",
                        "object_names": ["apple"],
                        "questions": ["What color is it?", "Is it ripe?"]
                    }
                })
            }
        ]
    }
)

result = response.json()
print(result["choices"][0]["message"]["content"])
```

## Function Call Format

All perception functions are called using JSON format in the message content:

```json
{
    "function": "function_name",
    "arguments": {
        "image_type": "2D",
        "image_url": "https://example.com/image.jpg",
        "object_names": ["object1", "object2"],
        "depth_url": "https://example.com/depth.jpg",
        "questions": ["question1", "question2"]
    }
}
```

### Required Parameters

- `image_type`: "2D" or "3D"
- `image_url`: URL of the image to analyze
- `object_names`: Array of object names to detect/analyze

### Optional Parameters

- `depth_url`: Required for 3D image processing
- `questions`: Required for `props_describe` and `full_perception`

## Response Format

Responses follow OpenAI's chat completion format:

```json
{
    "id": "chatcmpl-...",
    "object": "chat.completion",
    "created": 1234567890,
    "model": "qj-perception-v1",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Object Detection Results:\n- apple: confidence 0.95, bbox [10, 20, 100, 120]\n- banana: confidence 0.87, bbox [150, 30, 200, 140]"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 50,
        "completion_tokens": 30,
        "total_tokens": 80
    }
}
```

## Examples

See the example files:

- `openai_api_example.py` - Direct API usage examples
- `openai_client_example.py` - Client examples with HTTP requests
- `openai_api_server.py` - FastAPI server implementation

## Error Handling

The API provides proper error handling with descriptive messages:

```python
try:
    response = api.create_chat_completion(messages=messages)
except ValueError as e:
    print(f"Invalid parameters: {e}")
except Exception as e:
    print(f"API error: {e}")
```

## Integration with Existing Applications

This OpenAI-compatible interface makes it easy to integrate perception capabilities into existing applications that use OpenAI's API:

1. **Replace the API endpoint**: Point to your local server
2. **Use the same client libraries**: No code changes needed
3. **Format function calls**: Use JSON format for perception functions
4. **Handle responses**: Same response format as OpenAI

## Performance Considerations

- Use async mode for high-throughput applications
- Enable streaming for real-time user interfaces
- Configure appropriate worker counts based on your hardware
- Cache results when possible to reduce API calls

## Troubleshooting

1. **Server not starting**: Check if port 8000 is available
2. **Function call errors**: Verify JSON format and required parameters
3. **Image URL errors**: Ensure images are accessible and in supported formats
4. **Timeout errors**: Increase timeout values for complex images

For more examples and detailed usage, see the example files included with this package.
