#!/usr/bin/env python3
"""
Verify the reorganized project structure.

This script checks that all files are in their correct new locations
and that the project structure follows the new organization.
"""

import os
import sys
from pathlib import Path


def check_new_structure():
    """Check the new project structure"""
    print("=== Checking New Project Structure ===")
    
    expected_structure = {
        "py_qj_robots": [
            "__init__.py",
            "perception.py", 
            "authorization.py",
            "openai_api.py"
        ],
        "openai": [
            "__init__.py",
            "openai_api.py",
            "openai_api_server.py"
        ],
        "docs": [
            "OPENAI_API_README.md",
            "TOKEN_AUTHENTICATION_GUIDE.md",
            "INSTALLATION_GUIDE.md", 
            "QUICK_START_GUIDE.md",
            "DEPENDENCIES_SUMMARY.md",
            "PROJECT_STRUCTURE.md"
        ],
        "tests": [
            "__init__.py",
            "test_openai_api.py",
            "test_token_auth.py",
            "verify_installation.py"
        ],
        "examples": [
            "__init__.py",
            "openai_api_example.py",
            "openai_client_example.py",
            "async_usage_example.py"
        ],
        "utils": [
            "__init__.py",
            "token_config.py",
            "manage_tokens.py", 
            "install_dependencies.py"
        ],
        "conf": [
            "__init__.py",
            "requirements.txt",
            "requirements-openai.txt",
            "setup.py",
            "token_mapping.json"
        ],
        "output": [
            "__init__.py"
            # CSV, TXT, JSON files are optional
        ],
        "bin": [
            "start_server.sh",
            "run_tests.sh",
            "install_deps.sh",
            "manage_tokens.sh",
            "update-version.sh",
            "upload-pip.sh",
            "upload-test-pip.sh"
        ]
    }
    
    all_good = True
    
    for directory, files in expected_structure.items():
        if not os.path.exists(directory):
            print(f"❌ Directory missing: {directory}")
            all_good = False
            continue
        
        print(f"✅ Directory exists: {directory}")
        
        for file in files:
            file_path = os.path.join(directory, file)
            if os.path.exists(file_path):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} - MISSING")
                all_good = False
    
    # Check root files
    root_files = ["README.md", "LICENSE", "verify_structure.py"]
    
    print(f"\n✅ Root directory files:")
    for file in root_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - MISSING")
            all_good = False
    
    return all_good


def check_script_permissions():
    """Check that shell scripts have execute permissions"""
    print("\n=== Checking Script Permissions ===")
    
    scripts = [
        "bin/start_server.sh",
        "bin/run_tests.sh", 
        "bin/install_deps.sh",
        "bin/manage_tokens.sh"
    ]
    
    all_good = True
    
    for script in scripts:
        if os.path.exists(script):
            # Check if file is executable
            if os.access(script, os.X_OK):
                print(f"✅ {script} - executable")
            else:
                print(f"⚠️  {script} - not executable (run: chmod +x {script})")
                all_good = False
        else:
            print(f"❌ {script} - missing")
            all_good = False
    
    return all_good


def check_file_organization():
    """Check that files are properly organized"""
    print("\n=== Checking File Organization ===")
    
    checks = [
        ("OpenAI API server", "openai/openai_api_server.py"),
        ("Token configuration", "conf/token_mapping.json"),
        ("Requirements files", "conf/requirements-openai.txt"),
        ("Setup script", "conf/setup.py"),
        ("Shell scripts", "bin/start_server.sh"),
        ("Documentation", "docs/PROJECT_STRUCTURE.md"),
        ("Test scripts", "tests/test_openai_api.py"),
        ("Examples", "examples/openai_api_example.py"),
        ("Utilities", "utils/token_config.py")
    ]
    
    all_good = True
    
    for description, file_path in checks:
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ {description}: {file_path} - MISSING")
            all_good = False
    
    return all_good


def show_directory_tree():
    """Show the new directory structure"""
    print("\n=== New Directory Structure ===")
    
    def print_tree(directory, prefix="", max_depth=2, current_depth=0):
        if current_depth >= max_depth or not os.path.exists(directory):
            return
            
        items = sorted(os.listdir(directory))
        # Filter out hidden files, __pycache__, and .egg-info
        items = [item for item in items if not item.startswith('.') 
                and item != '__pycache__' and not item.endswith('.egg-info')]
        
        for i, item in enumerate(items):
            path = os.path.join(directory, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            
            # Add emoji for different types
            if os.path.isdir(path):
                if item == "py_qj_robots":
                    emoji = "🏗️ "
                elif item == "openai":
                    emoji = "🤖 "
                elif item == "docs":
                    emoji = "📚 "
                elif item == "tests":
                    emoji = "🧪 "
                elif item == "examples":
                    emoji = "💡 "
                elif item == "utils":
                    emoji = "🛠️ "
                elif item == "conf":
                    emoji = "⚙️ "
                elif item == "output":
                    emoji = "📊 "
                elif item == "bin":
                    emoji = "🚀 "
                else:
                    emoji = "📁 "
            else:
                emoji = "📄 "
            
            print(f"{prefix}{current_prefix}{emoji}{item}")
            
            if os.path.isdir(path) and current_depth < max_depth - 1:
                extension = "    " if is_last else "│   "
                print_tree(path, prefix + extension, max_depth, current_depth + 1)
    
    print("py-qj-robots/")
    print_tree(".", max_depth=3)


def main():
    """Run all verification checks"""
    print("QJ Robots Project Structure Verification (New Organization)")
    print("=" * 65)
    
    checks = [
        ("New Structure", check_new_structure),
        ("Script Permissions", check_script_permissions), 
        ("File Organization", check_file_organization)
    ]
    
    results = []
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
            results.append(False)
    
    # Show the new structure
    show_directory_tree()
    
    # Summary
    print("\n" + "=" * 65)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All {total} checks passed! New project structure is correct.")
        print("\n🚀 Quick Start Commands:")
        print("  ./bin/install_deps.sh     # Install dependencies")
        print("  ./bin/start_server.sh     # Start API server")
        print("  ./bin/run_tests.sh        # Run test suite")
        print("  ./bin/manage_tokens.sh list # Manage tokens")
        print("\n📁 Directory Organization:")
        print("  openai/     - OpenAI-compatible API implementation")
        print("  conf/       - Configuration files and requirements")
        print("  output/     - Test results and log files")
        print("  bin/        - Executable shell scripts")
        print("  docs/       - Documentation")
        print("  tests/      - Test scripts")
        print("  examples/   - Usage examples")
        print("  utils/      - Utility tools")
        return 0
    else:
        print(f"❌ {passed}/{total} checks passed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    exit(main())
