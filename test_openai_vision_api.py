#!/usr/bin/env python3
"""
OpenAI Vision API 测试脚本

使用 OpenAI 标准库测试图像识别功能，识别图片中的车辆数量和颜色。

使用方法:
    python test_openai_vision_api.py <image_path>

示例:
    python test_openai_vision_api.py ./test_image.jpg
    python test_openai_vision_api.py https://example.com/image.jpg
"""

import sys
import base64
import mimetypes
from pathlib import Path
import requests


# 导入标准 OpenAI 库
from openai import OpenAI

# API 配置
BASE_URL = "https://jwd.vooice.tech/v1"  # 通过 nginx 转发到本地端口 8000
API_KEY = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"

def encode_image_to_base64(image_path):
    """将图片编码为 base64 格式"""
    try:
        if image_path.startswith(('http://', 'https://')):
            # 处理网络图片
            response = requests.get(image_path)
            response.raise_for_status()
            image_data = response.content
            # 尝试从 Content-Type 获取 MIME 类型
            content_type = response.headers.get('content-type', '')
            if content_type.startswith('image/'):
                mime_type = content_type
            else:
                # 从 URL 推断 MIME 类型
                mime_type = mimetypes.guess_type(image_path)[0] or 'image/jpeg'
        else:
            # 处理本地图片
            image_path = Path(image_path)
            if not image_path.exists():
                raise FileNotFoundError(f"图片文件不存在: {image_path}")
            
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
            
            # 获取 MIME 类型
            mime_type = mimetypes.guess_type(str(image_path))[0]
            if not mime_type or not mime_type.startswith('image/'):
                mime_type = 'image/jpeg'  # 默认为 JPEG
        
        # 编码为 base64
        base64_image = base64.b64encode(image_data).decode('utf-8')
        return f"data:{mime_type};base64,{base64_image}"
    
    except Exception as e:
        print(f"❌ 图片编码失败: {e}")
        return None

def test_vision_api(image_path):
    """测试视觉 API"""
    print("🚀 开始测试 OpenAI Vision API...")
    print(f"📍 API 端点: {BASE_URL}")
    print(f"🖼️  图片路径: {image_path}")
    print("-" * 50)

    # 处理图片URL
    if image_path.startswith(('http://', 'https://')):
        print("📝 使用网络图片URL...")
        image_url = image_path
        print("✅ 图片URL准备完成")
    else:
        print("❌ 本地图片不支持，请使用网络图片URL")
        print("   示例: https://example.com/image.jpg")
        return False
    
    # 初始化 OpenAI 客户端
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )
    
    try:
        print("🔍 正在调用 API 分析图片...")
        
        # 构建消息 - 使用正确的 OpenAI 消息格式
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请仔细分析这张图片，告诉我图中有几辆车，分别是什么颜色？请详细描述每辆车的特征。"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }
        ]
        
        # 调用 API
        response = client.chat.completions.create(
            model="qj-perception-v1",
            messages=messages,  # type: ignore
            max_tokens=1000,
            temperature=0.7
        )
        
        # 输出结果
        print("✅ API 调用成功！")
        print("-" * 50)
        print("🤖 AI 分析结果:")
        #print(response.choices[0].message.content)  # 输出回复内容
        print("-" * 50)
        
        # 输出使用统计
        if hasattr(response, 'usage') and response.usage:
            print("📊 使用统计:")
            print(f"   输入 tokens: {response.usage.prompt_tokens}")
            print(f"   输出 tokens: {response.usage.completion_tokens}")
            print(f"   总计 tokens: {response.usage.total_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ API 调用失败: {e}")
        return False

def test_streaming_api(image_path):
    """测试流式 API"""
    print("\n🌊 开始测试流式 API...")
    print("-" * 50)

    # 处理图片URL
    if image_path.startswith(('http://', 'https://')):
        image_url = image_path
    else:
        print("❌ 本地图片不支持，请使用网络图片URL")
        return False

    # 初始化 OpenAI 客户端
    client = OpenAI(
        api_key=API_KEY,
        base_url=BASE_URL
    )

    try:
        print("🔍 正在调用流式 API...")

        # 构建消息 - 使用正确的 OpenAI 消息格式
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "请分析图片中的车辆，简要说明有几辆车，什么颜色。"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_url
                        }
                    }
                ]
            }
        ]

        # 调用流式 API
        stream = client.chat.completions.create(
            model="qj-perception-v1",
            messages=messages,  # type: ignore
            max_tokens=500,
            temperature=0.7,
            stream=True
        )

        print("🤖 AI 流式响应:")
        print("-" * 40)
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                print(chunk.choices[0].delta.content, end='', flush=True)

        print("\n" + "-" * 50)
        print("✅ 流式 API 测试完成！")
        return True

    except Exception as e:
        print(f"❌ 流式 API 调用失败: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python test_openai_vision_api.py <image_path>")
        print("示例:")
        print("  python test_openai_vision_api.py ./test_image.jpg")
        print("  python test_openai_vision_api.py https://example.com/image.jpg")
        sys.exit(1)
    
    image_path = sys.argv[1]
    
    print("🎯 OpenAI Vision API 测试工具")
    print("=" * 50)
    
    # 测试普通 API
    success1 = test_vision_api(image_path)
    
    # 测试流式 API
    success2 = test_streaming_api(image_path)
    
    # 总结
    print("\n📋 测试总结:")
    print(f"   普通 API: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   流式 API: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 or success2:
        print("\n🎉 测试完成！API 工作正常。")
    else:
        print("\n⚠️  所有测试都失败了，请检查配置和网络连接。")

if __name__ == "__main__":
    main()
