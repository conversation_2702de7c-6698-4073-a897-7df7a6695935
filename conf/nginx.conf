
#user  nobody;
worker_processes  2;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 5M;

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    server {
        listen       80;
        server_name  localhost;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

    location / {
        proxy_pass http://localhost:5173;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

    server {
        listen       443 ssl;
        server_name  localhost jwd.vooice.tech;

        ssl_certificate      /Users/<USER>/Downloads/fullchain.pem; 
#19481235_jwd.vooice.tech_nginx/jwd.vooice.tech.pem;
        ssl_certificate_key   /Users/<USER>/Downloads/privkey.pem; 
#19481235_jwd.vooice.tech_nginx/jwd.vooice.tech.key;
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  5m;

        ssl_ciphers  HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers  on;

        # OpenAI API 转发配置 - 转发 /v1 路径到本地 OpenAI API 服务器
        location /v1/ {
            # CORS 配置
            set $cors_origin "";
            if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
                set $cors_origin $http_origin;
            }

            # 预检请求处理
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' $cors_origin always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
                add_header 'Access-Control-Max-Age' 86400 always;
                add_header 'Content-Length' 0;
                return 204;
            }

            # 常规请求处理
            add_header 'Access-Control-Allow-Origin' $cors_origin always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # 转发到本地 OpenAI API 服务器 (端口 8000)
            proxy_pass http://localhost:8000/v1/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # 超时设置 - 适合 AI API 的较长响应时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;  # 5分钟，适合 AI 处理时间
        }

        # 感知API测试页面静态文件配置（前后端分离）
        location /perception/ {
            # 直接提供静态文件服务
            alias /opt/homebrew/var/www/perception/;
            index demo.html;

            # 设置静态文件缓存
            location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # 设置HTML文件缓存策略
            location ~* \.html$ {
                expires 1h;
                add_header Cache-Control "public, must-revalidate";
            }
        }

        # OpenAI API接口转发配置
        location /openai/ {
            # CORS 配置
            set $cors_origin "";
            if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
                set $cors_origin $http_origin;
            }

            # 预检请求处理
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' $cors_origin always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
                add_header 'Access-Control-Max-Age' 86400 always;
                add_header 'Content-Length' 0;
                return 204;
            }

            # 常规请求处理
            add_header 'Access-Control-Allow-Origin' $cors_origin always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # 去掉 /openai 前缀，转发到本地OpenAI API服务器 (端口 8000)
            rewrite ^/openai/(.*)$ /$1 break;
            proxy_pass http://localhost:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;

            # 超时设置 - 适合 AI API 的较长响应时间
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 300s;
        }

        location /open-api/ {
            set $cors_origin "";
            if ($http_origin ~* "^https?://(localhost|127\.0\.0\.1|.*\.vooice\.tech)") {
                set $cors_origin $http_origin;
            }

            # 预检请求处理
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' $cors_origin always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
                add_header 'Access-Control-Max-Age' 86400 always;
                add_header 'Content-Length' 0;
                return 204;
            }

            # 常规请求处理
            add_header 'Access-Control-Allow-Origin' $cors_origin always;

            proxy_pass http://localhost:9999/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }


        location / {
            proxy_pass http://localhost:5173;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }


        
    }

    include servers/*;
}
