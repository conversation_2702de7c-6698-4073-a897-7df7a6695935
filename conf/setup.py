from setuptools import setup, find_packages
import os

# 读取 README.md 文件，如果不存在则使用默认描述
readme_path = os.path.join(os.path.dirname(__file__), "..", "README.md")
if os.path.exists(readme_path):
    with open(readme_path, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = "QJ Python SDK for perception capabilities"

setup(
    name="py-qj-robots",
    version="0.1.13",
    author="QJ ROBOTS",
    author_email="<EMAIL>",
    description="QJ Python SDK",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/QJ-ROBOTS/perception-python-sdk",
    packages=find_packages(where=".."),
    package_dir={"": ".."},
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.0",
    install_requires=[
        "requests>=2.26.0",
        "python-dotenv>=0.19.0"
    ],
    extras_require={
        "openai": [
            "fastapi>=0.68.0",
            "uvicorn[standard]>=0.15.0",
            "pydantic>=1.8.0"
        ],
        "dev": [
            "fastapi>=0.68.0",
            "uvicorn[standard]>=0.15.0",
            "pydantic>=1.8.0",
            "pytest>=6.0.0",
            "pytest-asyncio>=0.18.0",
            "httpx>=0.23.0"
        ]
    },
)