# QJ Robots OpenAI API - Complete Dependencies
# Install with: pip install -r requirements-openai.txt

# Core QJ Robots dependencies
requests>=2.26.0
python-dotenv>=0.19.0

# OpenAI API compatibility dependencies
fastapi>=0.68.0
uvicorn[standard]>=0.15.0
pydantic>=1.8.0

# Optional: OpenAI client library for examples and testing
# Uncomment the line below if you want to run OpenAI client examples
# openai>=0.27.0
