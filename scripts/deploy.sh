#!/bin/bash
"""
QJ Robots 感知API部署脚本
自动化部署和健康检查
"""

set -e  # 遇到错误立即退出

# 配置变量
PROJECT_ROOT="/Users/<USER>/cursor-tutor/projects/python3/py-qj-robots"
OPENAI_SERVER_DIR="$PROJECT_ROOT/openai-server"
BACKUP_DIR="$PROJECT_ROOT/backups"
LOG_DIR="$PROJECT_ROOT/logs"
PID_FILE="$PROJECT_ROOT/server.pid"
PORT=8000

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/tests"
    mkdir -p "$PROJECT_ROOT/docs"
}

# 备份当前版本
backup_current_version() {
    log_info "备份当前版本..."
    
    if [ -f "$OPENAI_SERVER_DIR/openai_api_server.py" ]; then
        TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
        BACKUP_FILE="$BACKUP_DIR/openai_api_server_$TIMESTAMP.py"
        cp "$OPENAI_SERVER_DIR/openai_api_server.py" "$BACKUP_FILE"
        log_success "已备份到: $BACKUP_FILE"
    else
        log_warning "未找到现有服务器文件，跳过备份"
    fi
}

# 部署新版本
deploy_new_version() {
    log_info "部署新版本..."
    
    cd "$OPENAI_SERVER_DIR"
    
    # 检查重构版本是否存在
    if [ -f "openai_api_server_refactored.py" ]; then
        cp "openai_api_server_refactored.py" "openai_api_server.py"
        log_success "已部署重构版本"
    else
        log_error "未找到重构版本文件"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查Python依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 检查Python版本
    PYTHON_VERSION=$(python3 --version 2>&1)
    log_info "Python版本: $PYTHON_VERSION"
    
    # 检查必要的包
    REQUIRED_PACKAGES=("fastapi" "uvicorn" "pydantic")
    
    for package in "${REQUIRED_PACKAGES[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            log_success "✓ $package 已安装"
        else
            log_error "✗ $package 未安装"
            log_info "正在安装 $package..."
            pip3 install "$package"
        fi
    done
}

# 运行测试
run_tests() {
    log_info "运行测试套件..."
    
    cd "$OPENAI_SERVER_DIR"
    
    # 运行阶段1测试
    log_info "运行阶段1测试..."
    if cd "$PROJECT_ROOT" && python3 tests/test_stage1_refactoring.py; then
        log_success "✓ 阶段1测试通过"
    else
        log_error "✗ 阶段1测试失败"
        return 1
    fi

    # 运行阶段2测试
    log_info "运行阶段2测试..."
    if cd "$PROJECT_ROOT" && python3 tests/test_stage2_function_calling.py; then
        log_success "✓ 阶段2测试通过"
    else
        log_error "✗ 阶段2测试失败"
        return 1
    fi
    
    log_success "所有测试通过！"
}

# 启动服务器
start_server() {
    log_info "启动服务器..."
    
    cd "$OPENAI_SERVER_DIR"
    
    # 检查端口是否被占用
    if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null; then
        log_warning "端口 $PORT 已被占用，尝试停止现有服务..."
        stop_server
        sleep 2
    fi
    
    # 启动服务器
    nohup python3 openai_api_server.py > "$LOG_DIR/server.log" 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > "$PID_FILE"
    
    log_info "服务器已启动，PID: $SERVER_PID"
    log_info "日志文件: $LOG_DIR/server.log"
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否正常启动
    if health_check; then
        log_success "服务器启动成功！"
        log_info "服务器地址: http://localhost:$PORT"
        log_info "API文档: http://localhost:$PORT/docs"
    else
        log_error "服务器启动失败"
        return 1
    fi
}

# 停止服务器
stop_server() {
    log_info "停止服务器..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            kill "$PID"
            log_success "服务器已停止 (PID: $PID)"
            rm -f "$PID_FILE"
        else
            log_warning "服务器进程不存在 (PID: $PID)"
            rm -f "$PID_FILE"
        fi
    else
        log_warning "未找到PID文件，尝试通过端口停止..."
        PID=$(lsof -Pi :$PORT -sTCP:LISTEN -t)
        if [ -n "$PID" ]; then
            kill "$PID"
            log_success "已通过端口停止服务器 (PID: $PID)"
        else
            log_info "未找到运行中的服务器"
        fi
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查服务器是否响应
    if curl -s -f "http://localhost:$PORT/health" > /dev/null; then
        log_success "✓ 健康检查通过"
        return 0
    else
        log_error "✗ 健康检查失败"
        return 1
    fi
}

# 显示服务器状态
show_status() {
    log_info "检查服务器状态..."
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            log_success "服务器正在运行 (PID: $PID)"
            
            # 显示详细信息
            echo "服务器信息:"
            echo "  - PID: $PID"
            echo "  - 端口: $PORT"
            echo "  - 日志: $LOG_DIR/server.log"
            echo "  - API文档: http://localhost:$PORT/docs"
            
            # 执行健康检查
            if health_check; then
                echo "  - 状态: 健康"
            else
                echo "  - 状态: 异常"
            fi
        else
            log_warning "PID文件存在但进程不存在"
            rm -f "$PID_FILE"
        fi
    else
        log_info "服务器未运行"
    fi
}

# 显示日志
show_logs() {
    if [ -f "$LOG_DIR/server.log" ]; then
        log_info "显示服务器日志 (最后50行):"
        tail -n 50 "$LOG_DIR/server.log"
    else
        log_warning "未找到日志文件"
    fi
}

# 完整部署流程
full_deploy() {
    log_info "开始完整部署流程..."
    
    create_directories
    backup_current_version
    check_dependencies
    deploy_new_version
    
    if run_tests; then
        stop_server
        start_server
        log_success "部署完成！"
    else
        log_error "测试失败，部署中止"
        exit 1
    fi
}

# 回滚到备份版本
rollback() {
    log_info "回滚到备份版本..."
    
    # 找到最新的备份文件
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/openai_api_server_*.py 2>/dev/null | head -n 1)
    
    if [ -n "$LATEST_BACKUP" ]; then
        log_info "回滚到: $LATEST_BACKUP"
        cp "$LATEST_BACKUP" "$OPENAI_SERVER_DIR/openai_api_server.py"
        
        stop_server
        start_server
        
        log_success "回滚完成！"
    else
        log_error "未找到备份文件"
        exit 1
    fi
}

# 主函数
main() {
    case "${1:-}" in
        "deploy")
            full_deploy
            ;;
        "start")
            start_server
            ;;
        "stop")
            stop_server
            ;;
        "restart")
            stop_server
            sleep 2
            start_server
            ;;
        "status")
            show_status
            ;;
        "health")
            health_check
            ;;
        "logs")
            show_logs
            ;;
        "test")
            run_tests
            ;;
        "rollback")
            rollback
            ;;
        *)
            echo "QJ Robots 感知API部署脚本"
            echo ""
            echo "用法: $0 {deploy|start|stop|restart|status|health|logs|test|rollback}"
            echo ""
            echo "命令说明:"
            echo "  deploy   - 完整部署流程（备份、测试、部署、启动）"
            echo "  start    - 启动服务器"
            echo "  stop     - 停止服务器"
            echo "  restart  - 重启服务器"
            echo "  status   - 显示服务器状态"
            echo "  health   - 执行健康检查"
            echo "  logs     - 显示服务器日志"
            echo "  test     - 运行测试套件"
            echo "  rollback - 回滚到最新备份版本"
            echo ""
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
