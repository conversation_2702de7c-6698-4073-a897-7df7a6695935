#!/usr/bin/env python3
"""
阶段1重构测试脚本
测试PerceptionHandler和ResponseGenerator的功能
"""

import sys
import os
import unittest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'openai-server'))

from perception_handler import PerceptionHandler
from response_generator import ResponseGenerator, FunctionCallResponseGenerator


class TestPerceptionHandler(unittest.TestCase):
    """测试PerceptionHandler类"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.handler = PerceptionHandler(self.logger)
    
    def test_determine_function_by_keywords(self):
        """测试基于关键词的函数选择"""
        test_cases = [
            ("split this image", "split_image"),
            ("describe the object", "props_describe"),
            ("what is the angle of this object", "angle_prediction"),
            ("find keypoints", "key_point_prediction"),
            ("where to grab", "grab_point_prediction"),
            ("full analysis", "full_perception"),
            ("detect objects", "check_image"),  # 默认
            ("random text", "check_image"),     # 默认
        ]
        
        for text, expected_function in test_cases:
            with self.subTest(text=text):
                function_name, params = self.handler.determine_function(text)
                self.assertEqual(function_name, expected_function)
                self.assertIsInstance(params, dict)
    
    def test_determine_function_with_explicit_call(self):
        """测试显式指定函数调用"""
        # 测试字符串格式的function_call
        function_name, params = self.handler.determine_function(
            "test text", 
            functions=None,
            function_call="split_image"
        )
        self.assertEqual(function_name, "split_image")
        
        # 测试字典格式的function_call
        function_name, params = self.handler.determine_function(
            "test text",
            functions=[{"name": "props_describe", "parameters": {"questions": ["test"]}}],
            function_call={"name": "props_describe"}
        )
        self.assertEqual(function_name, "props_describe")
        self.assertEqual(params, {"questions": ["test"]})
    
    def test_extract_object_names(self):
        """测试对象名称提取"""
        test_cases = [
            ("I see a car and a person", ["car", "person"]),
            ("There is a building", ["building"]),
            ("Show me the vehicle", ["vehicle"]),
            ("Random text", []),
        ]
        
        for text, expected_objects in test_cases:
            with self.subTest(text=text):
                objects = self.handler._extract_object_names_from_text(text)
                self.assertEqual(objects, expected_objects)
    
    @patch('perception_handler.Perception')
    async def test_process_request(self):
        """测试请求处理"""
        # 创建模拟的感知API
        mock_perception = Mock()
        mock_perception.check_image = AsyncMock(return_value={"detected": ["car"]})
        
        # 测试处理请求
        result = await self.handler.process_request(
            mock_perception,
            "http://example.com/image.jpg",
            "detect cars",
            "check_image",
            {}
        )
        
        # 验证结果
        self.assertIsInstance(result, str)
        self.assertIn("对象检测结果", result)
        mock_perception.check_image.assert_called_once()
    
    def test_function_map_completeness(self):
        """测试函数映射的完整性"""
        expected_functions = [
            "check_image",
            "split_image", 
            "props_describe",
            "angle_prediction",
            "key_point_prediction",
            "grab_point_prediction",
            "full_perception"
        ]
        
        for func_name in expected_functions:
            with self.subTest(function=func_name):
                self.assertIn(func_name, self.handler.function_map)
                self.assertTrue(callable(self.handler.function_map[func_name]))


class TestResponseGenerator(unittest.TestCase):
    """测试ResponseGenerator类"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.perception_handler = Mock()
        self.generator = ResponseGenerator(self.perception_handler, self.logger)
    
    def test_create_completion_id(self):
        """测试完成ID生成"""
        completion_id = self.generator.create_completion_id()
        self.assertTrue(completion_id.startswith("chatcmpl-"))
        self.assertTrue(completion_id.startswith("chatcmpl-"))
        self.assertGreaterEqual(len(completion_id), 36)  # chatcmpl- + 至少29字符
    
    async def test_create_non_streaming_response(self):
        """测试非流式响应生成"""
        # 模拟请求
        mock_request = Mock()
        mock_request.model = "qj-perception-v1"
        
        # 模拟感知处理器
        self.perception_handler.process_request = AsyncMock(
            return_value="Test response content"
        )
        
        # 创建响应
        response = await self.generator.create_non_streaming_response(
            mock_request,
            Mock(),  # perception
            "http://example.com/image.jpg",
            "test text",
            "check_image",
            {}
        )
        
        # 验证响应格式
        self.assertIn("id", response)
        self.assertEqual(response["object"], "chat.completion")
        self.assertEqual(response["model"], "qj-perception-v1")
        self.assertIn("choices", response)
        self.assertIn("usage", response)
        
        # 验证消息内容
        choice = response["choices"][0]
        self.assertEqual(choice["message"]["role"], "assistant")
        self.assertEqual(choice["message"]["content"], "Test response content")
        self.assertEqual(choice["finish_reason"], "stop")
    
    def test_create_chunk_data(self):
        """测试chunk数据创建"""
        chunk_data = self.generator._create_chunk_data(
            "test-id",
            "test-model", 
            1234567890,
            {"content": "test content"}
        )
        
        # 验证格式
        self.assertTrue(chunk_data.startswith("data: "))
        self.assertTrue(chunk_data.endswith("\n\n"))
        
        # 解析JSON
        json_str = chunk_data[6:-2]  # 移除 "data: " 和 "\n\n"
        chunk_obj = json.loads(json_str)
        
        self.assertEqual(chunk_obj["id"], "test-id")
        self.assertEqual(chunk_obj["object"], "chat.completion.chunk")
        self.assertEqual(chunk_obj["model"], "test-model")
        self.assertEqual(chunk_obj["choices"][0]["delta"]["content"], "test content")


class TestFunctionCallResponseGenerator(unittest.TestCase):
    """测试FunctionCallResponseGenerator类"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.generator = FunctionCallResponseGenerator(self.logger)
    
    def test_create_function_call_response(self):
        """测试函数调用响应创建"""
        response = self.generator.create_function_call_response(
            "test-id",
            "test-model",
            "check_image",
            {"image_url": "http://example.com/image.jpg", "object_names": ["car"]}
        )
        
        # 验证响应格式
        self.assertEqual(response["id"], "test-id")
        self.assertEqual(response["object"], "chat.completion")
        self.assertEqual(response["model"], "test-model")
        
        # 验证函数调用
        choice = response["choices"][0]
        self.assertEqual(choice["message"]["role"], "assistant")
        self.assertIsNone(choice["message"]["content"])
        self.assertEqual(choice["finish_reason"], "function_call")
        
        function_call = choice["message"]["function_call"]
        self.assertEqual(function_call["name"], "check_image")
        
        # 验证参数
        arguments = json.loads(function_call["arguments"])
        self.assertEqual(arguments["image_url"], "http://example.com/image.jpg")
        self.assertEqual(arguments["object_names"], ["car"])
    
    def test_create_function_call_chunk(self):
        """测试函数调用chunk创建"""
        chunk_data = self.generator.create_function_call_chunk(
            "test-id",
            "test-model",
            "split_image",
            {"image_url": "http://example.com/image.jpg"}
        )
        
        # 验证格式
        self.assertTrue(chunk_data.startswith("data: "))
        self.assertTrue(chunk_data.endswith("\n\n"))
        
        # 解析JSON
        json_str = chunk_data[6:-2]
        chunk_obj = json.loads(json_str)
        
        self.assertEqual(chunk_obj["object"], "chat.completion.chunk")
        self.assertEqual(chunk_obj["choices"][0]["finish_reason"], "function_call")
        
        function_call = chunk_obj["choices"][0]["delta"]["function_call"]
        self.assertEqual(function_call["name"], "split_image")


class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.perception_handler = PerceptionHandler(self.logger)
        self.response_generator = ResponseGenerator(self.perception_handler, self.logger)
    
    def test_end_to_end_function_selection(self):
        """测试端到端的函数选择"""
        test_cases = [
            ("Please split this image into segments", "split_image"),
            ("Describe what you see in detail", "props_describe"),
            ("What's the angle of this object?", "angle_prediction"),
            ("Find the keypoints", "key_point_prediction"),
            ("Where should the robot grab this?", "grab_point_prediction"),
            ("Give me a full analysis", "full_perception"),
        ]
        
        for text, expected_function in test_cases:
            with self.subTest(text=text):
                function_name, params = self.perception_handler.determine_function(text)
                self.assertEqual(function_name, expected_function)


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行阶段1重构测试...")
    print("=" * 50)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestPerceptionHandler))
    suite.addTests(loader.loadTestsFromTestCase(TestResponseGenerator))
    suite.addTests(loader.loadTestsFromTestCase(TestFunctionCallResponseGenerator))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 50)
    if result.wasSuccessful():
        print("✅ 所有测试通过！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    else:
        print("❌ 测试失败！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   失败: {len(result.failures)}")
        print(f"   错误: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
