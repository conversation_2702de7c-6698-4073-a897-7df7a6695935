import threading
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

def test_2d_image_detection():
    # 模拟测试函数，实际应替换为真实测试逻辑
    # 返回是否成功，耗时（秒）
    time.sleep(2)  # 模拟处理时间
    return True, 2.0

def run_stress_test(concurrency, rounds):
    """
    压力测试函数

    :param concurrency: 并发度
    :param rounds: 执行轮次
    :return: 测试报告
    """
    total_requests = concurrency * rounds
    results = []
    success_count = 0
    failure_count = 0
    latencies = []

    def worker():
        nonlocal success_count, failure_count
        for _ in range(rounds):
            start_time = time.time()
            try:
                success, latency = test_2d_image_detection()
                latencies.append(latency)
                if success:
                    success_count += 1
                else:
                    failure_count += 1
            except Exception as e:
                failure_count += 1
                latencies.append(0)
                print(f"Error: {e}")

    start_time = time.time()

    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(worker) for _ in range(concurrency)]

        for future in futures:
            future.result()

    end_time = time.time()

    # 计算指标
    total_time = end_time - start_time
    min_latency = min(latencies) if latencies else 0
    max_latency = max(latencies) if latencies else 0
    avg_latency = statistics.mean(latencies) if latencies else 0
    
    # 计算QPS
    qps = total_requests / total_time if total_time > 0 else 0
    # 计算TPS
    tps = success_count / total_time if total_time > 0 else 0

    # 耗时分布
    latency_distribution = {
        'lt_3': 0,
        '3_to_5': 0,
        '5_to_10': 0,
        'gt_10': 0
    }

    for latency in latencies:
        if latency < 3:
            latency_distribution['lt_3'] += 1
        elif 3 <= latency < 5:
            latency_distribution['3_to_5'] += 1
        elif 5 <= latency < 10:
            latency_distribution['5_to_10'] += 1
        else:
            latency_distribution['gt_10'] += 1

    # 计算占比
    latency_distribution_percentage = {
        key: (value / total_requests * 100) if total_requests > 0 else 0
        for key, value in latency_distribution.items()
    }

    report = {
        'test_function': 'test_2d_image_detection',
        'rounds': rounds,
        'concurrency': concurrency,
        'total_requests': total_requests,
        'success_count': success_count,
        'failure_count': failure_count,
        'min_latency': min_latency,
        'max_latency': max_latency,
        'avg_latency': avg_latency,
        'latency_distribution': latency_distribution,
        'latency_distribution_percentage': latency_distribution_percentage,
        'total_time': total_time,
        'qps': qps,
        'tps': tps
    }

    return report

if __name__ == '__main__':
    # 示例：执行压力测试，并发度10，执行轮次5
    report = run_stress_test(concurrency=50, rounds=5)
    
    # 打印测试报告
    print("测试报告:")
    print(f"测试函数名: {report['test_function']}")
    print(f"执行轮次: {report['rounds']}")
    print(f"并发度: {report['concurrency']}")
    print(f"请求次数: {report['total_requests']}")
    print(f"成功次数: {report['success_count']}")
    print(f"失败次数: {report['failure_count']}")
    print(f"最小耗时: {report['min_latency']:.2f} 秒")
    print(f"最大耗时: {report['max_latency']:.2f} 秒")
    print(f"平均耗时: {report['avg_latency']:.2f} 秒")
    print(f"总耗时: {report['total_time']:.2f} 秒")
    print("耗时分布次数及占比:")
    for key, value in report['latency_distribution'].items():
        percentage = report['latency_distribution_percentage'][key]
        print(f"  {key}: {value} 次, {percentage:.2f}%")