#!/usr/bin/env python3
"""
阶段2 Function Calling 测试脚本
测试OpenAI Function Calling标准实现
"""

import sys
import os
import unittest
import json
from unittest.mock import Mock, AsyncMock, patch

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'openai-server'))

from function_call_manager import FunctionCallManager, FunctionCallDecision
from perception_handler import PerceptionHandler
from response_generator import ResponseGenerator


class TestFunctionCallManager(unittest.TestCase):
    """测试FunctionCallManager类"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.manager = FunctionCallManager(self.logger)
    
    def test_function_definitions_completeness(self):
        """测试函数定义的完整性"""
        expected_functions = [
            "check_image", "split_image", "props_describe",
            "angle_prediction", "key_point_prediction", 
            "grab_point_prediction", "full_perception"
        ]
        
        function_names = [f["name"] for f in self.manager.function_definitions]
        
        for expected_func in expected_functions:
            with self.subTest(function=expected_func):
                self.assertIn(expected_func, function_names)
    
    def test_should_call_function_with_none(self):
        """测试function_call="none"模式"""
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "分析这张图片"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        decision = self.manager.should_call_function(messages, function_call="none")
        
        self.assertFalse(decision.should_call)
        self.assertEqual(decision.reason, "Function calling disabled")
    
    def test_should_call_function_with_explicit_name(self):
        """测试显式指定函数名"""
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "分析这张图片"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        decision = self.manager.should_call_function(
            messages, 
            function_call={"name": "split_image"}
        )
        
        self.assertTrue(decision.should_call)
        self.assertEqual(decision.function_name, "split_image")
        self.assertIn("image_url", decision.arguments)
    
    def test_should_call_function_auto_mode(self):
        """测试auto模式的智能选择"""
        test_cases = [
            ("请分割这张图片", "split_image"),
            ("描述一下这个对象", "props_describe"),
            ("这个物体的角度是多少", "angle_prediction"),
            ("找到关键点", "key_point_prediction"),
            ("机器人应该从哪里抓取", "grab_point_prediction"),
            ("全面分析这张图片", "full_perception"),
            ("检测图片中的汽车", "check_image"),
        ]
        
        for text, expected_function in test_cases:
            with self.subTest(text=text):
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": text},
                            {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                        ]
                    }
                ]
                
                decision = self.manager.should_call_function(messages, function_call="auto")
                
                self.assertTrue(decision.should_call)
                self.assertEqual(decision.function_name, expected_function)
    
    def test_extract_image_url(self):
        """测试图片URL提取"""
        message = {
            "role": "user",
            "content": [
                {"type": "text", "text": "分析这张图片"},
                {"type": "image_url", "image_url": {"url": "https://example.com/test.jpg"}}
            ]
        }
        
        image_url = self.manager._extract_image_url(message)
        self.assertEqual(image_url, "https://example.com/test.jpg")
    
    def test_extract_object_names(self):
        """测试对象名称提取"""
        test_cases = [
            ("我看到一辆汽车和一个人", ["汽车", "人"]),  # 中文对象名称
            ("这里有一栋建筑", ["建筑"]),
            ("检测所有动物", ["动物"]),
            ("I see a car and a person", ["car", "person"]),  # 英文对象名称
            ("There is a building", ["building"]),
            ("Detect all animals", ["animal"]),
            ("随机文本", []),
        ]

        for text, expected_objects in test_cases:
            with self.subTest(text=text):
                objects = self.manager._extract_object_names(text)
                # 检查是否包含预期的对象（可能有额外的对象）
                for expected_obj in expected_objects:
                    self.assertIn(expected_obj, objects)
    
    def test_validate_function_call(self):
        """测试函数调用验证"""
        # 有效的函数调用
        is_valid, msg = self.manager.validate_function_call(
            "check_image",
            {
                "image_url": "https://example.com/image.jpg",
                "object_names": ["car", "person"]
            }
        )
        self.assertTrue(is_valid)
        
        # 缺少必需参数
        is_valid, msg = self.manager.validate_function_call(
            "check_image",
            {"image_url": "https://example.com/image.jpg"}
        )
        self.assertFalse(is_valid)
        self.assertIn("Missing required parameter", msg)
        
        # 不存在的函数
        is_valid, msg = self.manager.validate_function_call(
            "nonexistent_function",
            {}
        )
        self.assertFalse(is_valid)
        self.assertIn("not found", msg)


class TestPerceptionHandlerFunctionCalling(unittest.TestCase):
    """测试PerceptionHandler的Function Calling支持"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.handler = PerceptionHandler(self.logger)
    
    def test_get_function_definitions(self):
        """测试获取函数定义"""
        definitions = self.handler.get_function_definitions()
        
        self.assertIsInstance(definitions, list)
        self.assertGreater(len(definitions), 0)
        
        # 检查每个定义的结构
        for func_def in definitions:
            self.assertIn("name", func_def)
            self.assertIn("description", func_def)
            self.assertIn("parameters", func_def)
    
    def test_should_call_function(self):
        """测试函数调用判断"""
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "分析这张图片"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        decision = self.handler.should_call_function(messages)
        self.assertIsInstance(decision, FunctionCallDecision)
    
    @patch('perception_handler.Perception')
    async def test_execute_function_call(self):
        """测试函数调用执行"""
        mock_perception = Mock()
        mock_perception.check_image = AsyncMock(return_value={"detected": ["car"]})
        
        result = await self.handler.execute_function_call(
            mock_perception,
            "check_image",
            {
                "image_url": "https://example.com/image.jpg",
                "object_names": ["car"]
            }
        )
        
        self.assertIsNotNone(result)
        mock_perception.check_image.assert_called_once()
    
    def test_format_function_result(self):
        """测试函数结果格式化"""
        result = {
            "taskStatus": "DONE",
            "taskResult": {
                "labels": ["car", "person"],
                "scores": [0.9, 0.8]
            }
        }
        
        formatted = self.handler.format_function_result(result, "check_image", "检测汽车")
        
        self.assertIsInstance(formatted, str)
        self.assertIn("检测结果", formatted)
        self.assertIn("car", formatted)


class TestResponseGeneratorFunctionCalling(unittest.TestCase):
    """测试ResponseGenerator的Function Calling支持"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.perception_handler = Mock()
        self.generator = ResponseGenerator(self.perception_handler, self.logger)
    
    async def test_create_function_calling_response(self):
        """测试Function Calling响应创建"""
        # 模拟需要调用函数的决策
        mock_decision = FunctionCallDecision(
            should_call=True,
            function_name="check_image",
            arguments={"image_url": "https://example.com/image.jpg", "object_names": ["car"]},
            reason="Auto-selected check_image"
        )
        
        self.perception_handler.should_call_function.return_value = mock_decision
        
        # 模拟请求
        mock_request = Mock()
        mock_request.model = "qj-perception-v1"
        mock_request.functions = []
        mock_request.function_call = "auto"
        
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "检测汽车"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        response = await self.generator.create_function_calling_response(
            mock_request, Mock(), messages
        )
        
        # 验证响应格式
        self.assertIn("id", response)
        self.assertEqual(response["object"], "chat.completion")
        self.assertEqual(response["choices"][0]["finish_reason"], "function_call")
        
        # 验证函数调用信息
        function_call = response["choices"][0]["message"]["function_call"]
        self.assertEqual(function_call["name"], "check_image")
        
        # 验证参数
        arguments = json.loads(function_call["arguments"])
        self.assertEqual(arguments["image_url"], "https://example.com/image.jpg")
        self.assertEqual(arguments["object_names"], ["car"])


def run_function_calling_tests():
    """运行Function Calling测试"""
    print("🧪 开始运行阶段2 Function Calling测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestFunctionCallManager))
    suite.addTests(loader.loadTestsFromTestCase(TestPerceptionHandlerFunctionCalling))
    suite.addTests(loader.loadTestsFromTestCase(TestResponseGeneratorFunctionCalling))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有Function Calling测试通过！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    else:
        print("❌ Function Calling测试失败！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   失败: {len(result.failures)}")
        print(f"   错误: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_function_calling_tests()
    sys.exit(0 if success else 1)
