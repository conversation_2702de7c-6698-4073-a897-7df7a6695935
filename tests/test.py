import os
import time
import threading
import concurrent.futures
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from dotenv import load_dotenv
from py_qj_robots import Perception


def init_perception():
    # 加载环境变量
    load_dotenv()

    # 检查必要的环境变量
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')

    # 初始化Perception实例
    return Perception()


def run_test(func, perception):
    """通用测试函数包装器，用于运行测试并记录耗时"""
    start_time = time.time()
    print(f"\n开始执行 {func.__name__}")
    try:
        success = func(perception)
        status = "成功" if success else "失败"
    except Exception as e:
        status = f"异常: {str(e)}"
    finally:
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"结束执行 {func.__name__}, 耗时: {elapsed_time:.2f} 秒\n")
    return elapsed_time


def run_test_concurrent(func, perception):
    """并发测试函数包装器，用于运行测试并记录耗时"""
    start_time = time.time()
    result = {}
    print(f"开始执行 {func.__name__} 在线程 {threading.get_ident()}")
    try:
        success = func(perception)
        status = "成功" if success else "失败"
        result['success'] = success
    except Exception as e:
        status = f"异常: {str(e)}"
        result['success'] = False
    finally:
        end_time = time.time()
        elapsed_time = end_time - start_time
        result['elapsed_time'] = elapsed_time
        print(f"结束执行 {func.__name__}, 耗时: {elapsed_time:.2f} 秒\n")
    return result


def test_2d_image_detection(perception):
    # 测试2D图像检测
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]

    try:
        # 执行图像检测
        result = perception.check_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"检测结果: {result}")
        return True
    except Exception as e:
        print(f"检测失败: {str(e)}")
        return False


def test_3d_image_validation(perception):
    # 测试3D图像必须提供depth_url
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = "box"

    try:
        perception.check_image(
            image_type="3D",
            image_url=image_url,
            depth_url=image_url,
            object_names=object_names
        )
        print("错误：期望抛出ValueError异常但未抛出")
        return False
    except ValueError as e:
        print(f"验证成功：正确捕获到异常 - {str(e)}")
        return True
    except Exception as e:
        print(f"验证失败：捕获到意外异常 - {str(e)}")
        return False


def test_2d_image_split(perception):
    # 测试2D图像分割
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]

    try:
        # 执行图像分割
        result = perception.split_image(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"分割结果: {result}")
        return True
    except Exception as e:
        print(f"分割失败: {str(e)}")
        return False


def test_2d_image_props_describe(perception):
    # 测试2D图像属性问答
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]
    questions = ["这个物体是什么颜色的？"]

    try:
        # 执行属性问答
        result = perception.props_describe(
            image_type="2D",
            image_url=image_url,
            object_names=object_names,
            questions=questions
        )
        print(f"属性问答结果: {result}")
        return True
    except Exception as e:
        print(f"属性问答失败: {str(e)}")
        return False


def test_2d_image_angle_prediction(perception):
    # 测试2D图像角度预测
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]

    try:
        # 执行角度预测
        result = perception.angle_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"角度预测结果: {result}")
        return True
    except Exception as e:
        print(f"角度预测失败: {str(e)}")
        return False


def test_2d_image_grab_point_prediction(perception):
    # 测试2D图像抓取点检测
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]

    try:
        # 执行抓取点检测
        result = perception.grab_point_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"抓取点检测结果: {result}")
        return True
    except Exception as e:
        print(f"抓取点检测失败: {str(e)}")
        return False


def test_2d_image_key_point_prediction(perception):
    # 测试2D图像关键点检测
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]

    try:
        # 执行关键点检测
        result = perception.key_point_prediction(
            image_type="2D",
            image_url=image_url,
            object_names=object_names
        )
        print(f"关键点检测结果: {result}")
        return True
    except Exception as e:
        print(f"关键点检测失败: {str(e)}")
        return False


def test_2d_image_full_perception(perception):
    # 测试2D图像全功能感知
    image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
    object_names = ["bottle", "cup"]
    questions = ["这个物体是什么颜色的？"]

    try:
        # 执行全功能感知
        result = perception.full_perception(
            image_type="2D",
            image_url=image_url,
            object_names=object_names,
            questions=questions
        )
        print(f"全功能感知结果: {result}")
        return True
    except Exception as e:
        print(f"全功能感知失败: {str(e)}")
        return False


def run_test_group(perception, group_id):
    """运行一组串行测试，用于并发测试场景"""
    print(f"\n=== 开始执行测试组 {group_id} ===")
    test_functions = [
        test_2d_image_detection,
        # test_3d_image_validation,
        # test_2d_image_split,
        # test_2d_image_props_describe,
        # test_2d_image_angle_prediction,
        # test_2d_image_grab_point_prediction,
        # test_2d_image_key_point_prediction,
        # test_2d_image_full_perception
    ]
    for idx, func in enumerate(test_functions, start=1):
        print(f"测试组 {group_id} - 正在执行第 {idx}/{len(test_functions)} 个测试: {func.__name__}")
        run_test_concurrent(func, perception)
    print(f"=== 测试组 {group_id} 完成 ===")


def main():
    try:
        # 初始化感知实例
        perception = init_perception()
        # 简化并发测试逻辑
        test_func = test_2d_image_detection  # 指定要并发执行的测试函数
        num_threads = 5  # 指定并发线程数
        total_requests = 20  # 指定总请求次数，类似 ab -n

        print(f"\n=== 开始 {num_threads} 并发，共 {total_requests} 请求，执行 {test_func.__name__} ===")
        
        request_counter = 0  # 使用普通变量
        lock = threading.Lock()
        results = []
        success_count = 0
        failure_count = 0
        time_distribution = {'<3s': 0, '3-5s': 0, '5-10s': 0, '>10s': 0}

        def worker():
            nonlocal success_count, failure_count, results, time_distribution
            nonlocal request_counter  # 在这里声明request_counter为nonlocal
            local_results = []
            local_success = 0
            local_failure = 0
            local_time_distribution = {'<3s': 0, '3-5s': 0, '5-10s': 0, '>10s': 0}

            while True:
                with lock:
                    if request_counter >= total_requests:
                        # 更新全局变量
                        results.extend(local_results)
                        success_count += local_success
                        failure_count += local_failure
                        # 更新时间分布
                        for key in time_distribution:
                            time_distribution[key] += local_time_distribution[key]
                        return
                    # 使用 nonlocal 或 global 来更新计数器
                    request_counter += 1
                current_request = request_counter

                # 模拟测试执行
                try:
                    result = run_test_concurrent(test_func, perception)
                    elapsed_time = result['elapsed_time']
                    is_success = result['success']

                    local_results.append(elapsed_time)
                    if is_success:
                        local_success += 1
                    else:
                        local_failure += 1

                    # 耗时分布统计
                    if elapsed_time < 3:
                        local_time_distribution['<3s'] += 1
                    elif 3 <= elapsed_time < 5:
                        local_time_distribution['3-5s'] += 1
                    elif 5 <= elapsed_time < 10:
                        local_time_distribution['5-10s'] += 1
                    else:
                        local_time_distribution['>10s'] += 1

                except Exception as e:
                    print(f"执行过程中发生异常: {str(e)}")

        # 记录开始时间
        start_time = time.time()
        
        # 创建线程池
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker) for _ in range(num_threads)]
            all_results = []
            all_success = 0
            all_failure = 0
            all_time_distribution = {'<3s': 0, '3-5s': 0, '5-10s': 0, '>10s': 0}

            for future in concurrent.futures.as_completed(futures):
                try:
                    pass
                except Exception as e:
                    print(f"并发执行过程中发生异常: {str(e)}")
                    
        # 记录结束时间
        end_time = time.time()

        # 计算总耗时
        total_time = end_time - start_time
        
        # 计算TPS和QPS
        tps = total_requests / total_time if total_time > 0 else 0
        qps = total_requests / total_time if total_time > 0 else 0
        
        # 输出测试报告
        max_time = max(all_results) if all_results else 0
        min_time = min(all_results) if all_results else 0
        avg_time = sum(all_results) / len(all_results) if all_results else 0

        print("=== 测试报告 ===")
        print(f"测试函数: {test_func.__name__}")
        print(f"并发度: {num_threads}")
        print(f"请求次数: {total_requests}")
        print(f"成功次数: {all_success}")
        print(f"失败次数: {all_failure}")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"TPS: {tps:.2f}")
        print(f"QPS: {qps:.2f}")
        print(f"最大耗时: {max_time:.2f} 秒")
        print(f"最小耗时: {min_time:.2f} 秒")
        print(f"平均耗时: {avg_time:.2f} 秒")
        print("耗时分布:")
        print(f"  小于 3 秒: {all_time_distribution['<3s']} 次 ({(all_time_distribution['<3s'] / total_requests * 100):.2f}%)")
        print(f"  3 - 5 秒: {all_time_distribution['3-5s']} 次 ({(all_time_distribution['3-5s'] / total_requests * 100):.2f}%)")
        print(f"  5 - 10 秒: {all_time_distribution['5-10s']} 次 ({(all_time_distribution['5-10s'] / total_requests * 100):.2f}%)")
        print(f"  10 秒以上: {all_time_distribution['>10s']} 次 ({(all_time_distribution['>10s'] / total_requests * 100):.2f}%)")

        # 并发运行多个测试组
        num_groups = 3  # 设置并发的测试组数量
        print(f"\\n=== 开始 {num_groups} 组并发测试 ===")
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_groups) as executor:
            futures = [executor.submit(run_test_group, perception, group_id=i+1) for i in range(num_groups)]
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"测试组执行过程中发生异常: {str(e)}")

    except Exception as e:
        print(f"程序执行失败: {str(e)}")


if __name__ == '__main__':
    main()
