#!/usr/bin/env python3
"""
端到端集成测试
测试完整的API流程和Function Calling功能
"""

import sys
import os
import unittest
import json
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'openai-server'))

# 导入主应用
try:
    from openai_api_server_refactored import app
except ImportError:
    # 如果重构版本不存在，跳过测试
    print("⚠️ 重构版本不存在，跳过集成测试")
    sys.exit(0)


class TestEndToEndIntegration(unittest.TestCase):
    """端到端集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.client = TestClient(app)
        self.test_token = "test-token-123"
    
    def test_health_check(self):
        """测试健康检查端点"""
        response = self.client.get("/health")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["status"], "healthy")
        self.assertIn("timestamp", data)
    
    def test_list_models(self):
        """测试模型列表端点"""
        response = self.client.get("/v1/models")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["object"], "list")
        self.assertIn("data", data)
        self.assertGreater(len(data["data"]), 0)
        
        # 检查模型信息
        model = data["data"][0]
        self.assertEqual(model["id"], "qj-perception-v1")
        self.assertEqual(model["object"], "model")
        self.assertEqual(model["owned_by"], "qj-robots")
    
    def test_list_functions(self):
        """测试函数列表端点"""
        response = self.client.get("/v1/functions")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data["object"], "list")
        self.assertIn("data", data)
        
        # 检查是否包含所有7个函数
        function_names = [func["name"] for func in data["data"]]
        expected_functions = [
            "check_image", "split_image", "props_describe",
            "angle_prediction", "key_point_prediction", 
            "grab_point_prediction", "full_perception"
        ]
        
        for expected_func in expected_functions:
            self.assertIn(expected_func, function_names)
    
    @patch('openai_api_server_refactored.token_manager')
    def test_chat_completion_without_auth(self, mock_token_manager):
        """测试未授权的聊天完成请求"""
        response = self.client.post(
            "/v1/chat/completions",
            json={
                "model": "qj-perception-v1",
                "messages": [{"role": "user", "content": "Hello"}]
            }
        )
        
        self.assertEqual(response.status_code, 403)  # 没有Authorization header
    
    @patch('openai_api_server_refactored.token_manager')
    @patch('openai_api_server_refactored.Perception')
    def test_traditional_chat_completion(self, mock_perception_class, mock_token_manager):
        """测试传统模式的聊天完成"""
        # 设置mock
        mock_token_manager.get_credentials.return_value = ("test_app_id", "test_app_secret")
        mock_perception = Mock()
        mock_perception.check_image = AsyncMock(return_value={
            "taskStatus": "DONE",
            "taskResult": {
                "labels": ["car"],
                "scores": [0.95],
                "boxes": [[100, 200, 300, 400]]
            }
        })
        mock_perception_class.return_value = mock_perception
        
        response = self.client.post(
            "/v1/chat/completions",
            headers={"Authorization": f"Bearer {self.test_token}"},
            json={
                "model": "qj-perception-v1",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "检测这张图片中的汽车"},
                            {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
                        ]
                    }
                ],
                "stream": False
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证响应格式
        self.assertIn("id", data)
        self.assertEqual(data["object"], "chat.completion")
        self.assertEqual(data["model"], "qj-perception-v1")
        self.assertIn("choices", data)
        self.assertIn("usage", data)
        
        # 验证消息内容
        choice = data["choices"][0]
        self.assertEqual(choice["message"]["role"], "assistant")
        self.assertIn("检测结果", choice["message"]["content"])
        self.assertEqual(choice["finish_reason"], "stop")
    
    @patch('openai_api_server_refactored.token_manager')
    @patch('openai_api_server_refactored.os.environ', {})
    def test_function_calling_mode(self, mock_token_manager):
        """测试Function Calling模式"""
        mock_token_manager.get_credentials.return_value = ("test_app_id", "test_app_secret")
        
        response = self.client.post(
            "/v1/chat/completions",
            headers={"Authorization": f"Bearer {self.test_token}"},
            json={
                "model": "qj-perception-v1",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "请分析这张图片中的汽车"},
                            {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
                        ]
                    }
                ],
                "functions": [
                    {
                        "name": "check_image",
                        "description": "检测和定位图像中的对象",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "image_url": {"type": "string"},
                                "object_names": {"type": "array", "items": {"type": "string"}}
                            },
                            "required": ["image_url", "object_names"]
                        }
                    }
                ],
                "function_call": "auto"
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证Function Calling响应格式
        self.assertIn("id", data)
        self.assertEqual(data["object"], "chat.completion")
        self.assertIn("choices", data)
        
        choice = data["choices"][0]
        self.assertEqual(choice["message"]["role"], "assistant")
        self.assertIsNone(choice["message"]["content"])
        self.assertIn("function_call", choice["message"])
        self.assertEqual(choice["finish_reason"], "function_call")
        
        # 验证函数调用信息
        function_call = choice["message"]["function_call"]
        self.assertEqual(function_call["name"], "check_image")
        
        # 验证参数
        arguments = json.loads(function_call["arguments"])
        self.assertIn("image_url", arguments)
        self.assertIn("object_names", arguments)
        self.assertEqual(arguments["image_url"], "https://example.com/car.jpg")
    
    @patch('openai_api_server_refactored.token_manager')
    @patch('openai_api_server_refactored.os.environ', {})
    def test_function_result_processing(self, mock_token_manager):
        """测试函数结果处理"""
        mock_token_manager.get_credentials.return_value = ("test_app_id", "test_app_secret")
        
        # 模拟多轮对话：用户请求 -> 函数调用 -> 函数结果 -> 最终回复
        response = self.client.post(
            "/v1/chat/completions",
            headers={"Authorization": f"Bearer {self.test_token}"},
            json={
                "model": "qj-perception-v1",
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "请分析这张图片中的汽车"},
                            {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
                        ]
                    },
                    {
                        "role": "assistant",
                        "content": None,
                        "function_call": {
                            "name": "check_image",
                            "arguments": '{"image_url":"https://example.com/car.jpg","object_names":["car"]}'
                        }
                    },
                    {
                        "role": "function",
                        "name": "check_image",
                        "content": '{"taskStatus":"DONE","taskResult":{"labels":["car"],"scores":[0.95],"boxes":[[100,200,300,400]]}}'
                    }
                ]
            }
        )
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证最终响应
        choice = data["choices"][0]
        self.assertEqual(choice["message"]["role"], "assistant")
        self.assertIsNotNone(choice["message"]["content"])
        self.assertEqual(choice["finish_reason"], "stop")
        
        # 验证内容包含格式化的结果
        content = choice["message"]["content"]
        self.assertIn("检测结果", content)
        self.assertIn("car", content)
    
    def test_streaming_response_format(self):
        """测试流式响应格式（不实际执行，只验证格式）"""
        # 这个测试需要实际的流式响应实现
        # 目前只验证端点存在
        pass
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的请求格式
        response = self.client.post(
            "/v1/chat/completions",
            headers={"Authorization": f"Bearer {self.test_token}"},
            json={
                "model": "invalid-model",
                "messages": []  # 空消息列表
            }
        )
        
        # 应该返回错误状态码
        self.assertIn(response.status_code, [400, 422, 500])


class TestPerformanceBenchmark(unittest.TestCase):
    """性能基准测试"""
    
    def setUp(self):
        """测试初始化"""
        self.client = TestClient(app)
        self.test_token = "test-token-123"
    
    @patch('openai_api_server_refactored.token_manager')
    def test_response_time_benchmark(self, mock_token_manager):
        """测试响应时间基准"""
        mock_token_manager.get_credentials.return_value = ("test_app_id", "test_app_secret")
        
        start_time = time.time()
        
        response = self.client.get("/health")
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # 健康检查应该在100ms内完成
        self.assertLess(response_time, 0.1)
        self.assertEqual(response.status_code, 200)
    
    @patch('openai_api_server_refactored.token_manager')
    def test_concurrent_requests(self, mock_token_manager):
        """测试并发请求处理"""
        mock_token_manager.get_credentials.return_value = ("test_app_id", "test_app_secret")
        
        # 模拟10个并发健康检查请求
        import concurrent.futures
        
        def make_request():
            return self.client.get("/health")
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            responses = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 所有请求都应该成功
        for response in responses:
            self.assertEqual(response.status_code, 200)
        
        # 10个并发请求应该在2秒内完成
        self.assertLess(total_time, 2.0)
        
        print(f"✅ 10个并发请求完成时间: {total_time:.3f}秒")


def run_integration_tests():
    """运行集成测试"""
    print("🧪 开始运行端到端集成测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestEndToEndIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestPerformanceBenchmark))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有集成测试通过！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    else:
        print("❌ 集成测试失败！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   失败: {len(result.failures)}")
        print(f"   错误: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
