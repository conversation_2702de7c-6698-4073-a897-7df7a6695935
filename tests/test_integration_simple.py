#!/usr/bin/env python3
"""
简化的集成测试
专注于测试核心功能和组件集成
"""

import sys
import os
import unittest
import json
import time
from unittest.mock import Mock, AsyncMock, patch

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'openai-server'))

from perception_handler import PerceptionHandler
from response_generator import ResponseGenerator
from function_call_manager import FunctionCallManager
from performance_optimizer import PerceptionCache, RateLimiter, PerformanceMonitor


class TestComponentIntegration(unittest.TestCase):
    """组件集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.perception_handler = PerceptionHandler(self.logger)
        self.response_generator = ResponseGenerator(self.perception_handler, self.logger)
        self.function_call_manager = FunctionCallManager(self.logger)
    
    def test_function_call_manager_integration(self):
        """测试函数调用管理器集成"""
        # 测试智能函数选择
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "请分割这张图片"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                ]
            }
        ]
        
        decision = self.function_call_manager.should_call_function(messages, function_call="auto")
        
        self.assertTrue(decision.should_call)
        self.assertEqual(decision.function_name, "split_image")
        self.assertIn("image_url", decision.arguments)
        self.assertEqual(decision.arguments["image_url"], "https://example.com/image.jpg")
    
    def test_perception_handler_integration(self):
        """测试感知处理器集成"""
        # 测试函数定义获取
        definitions = self.perception_handler.get_function_definitions()
        
        self.assertIsInstance(definitions, list)
        self.assertEqual(len(definitions), 7)
        
        # 检查每个函数定义的结构
        for func_def in definitions:
            self.assertIn("name", func_def)
            self.assertIn("description", func_def)
            self.assertIn("parameters", func_def)
            
            # 检查参数结构
            params = func_def["parameters"]
            self.assertEqual(params["type"], "object")
            self.assertIn("properties", params)
            self.assertIn("required", params)
    
    def test_function_validation_integration(self):
        """测试函数验证集成"""
        # 测试有效的函数调用
        is_valid, msg = self.perception_handler.validate_function_call(
            "check_image",
            {
                "image_url": "https://example.com/image.jpg",
                "object_names": ["car", "person"]
            }
        )
        
        self.assertTrue(is_valid)
        self.assertEqual(msg, "Valid function call")
        
        # 测试无效的函数调用
        is_valid, msg = self.perception_handler.validate_function_call(
            "check_image",
            {"image_url": "https://example.com/image.jpg"}  # 缺少object_names
        )
        
        self.assertFalse(is_valid)
        self.assertIn("Missing required parameter", msg)
    
    @patch('perception_handler.Perception')
    async def test_function_execution_integration(self, mock_perception_class):
        """测试函数执行集成"""
        # 设置mock
        mock_perception = Mock()
        mock_perception.check_image = AsyncMock(return_value={
            "taskStatus": "DONE",
            "taskResult": {"labels": ["car"], "scores": [0.95]}
        })
        
        # 执行函数调用
        result = await self.perception_handler.execute_function_call(
            mock_perception,
            "check_image",
            {
                "image_url": "https://example.com/image.jpg",
                "object_names": ["car"]
            }
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result["taskStatus"], "DONE")
        mock_perception.check_image.assert_called_once()
    
    def test_result_formatting_integration(self):
        """测试结果格式化集成"""
        result = {
            "taskStatus": "DONE",
            "taskResult": {
                "labels": ["car", "person"],
                "scores": [0.95, 0.88]
            }
        }
        
        formatted = self.perception_handler.format_function_result(
            result, "check_image", "检测汽车和人"
        )
        
        self.assertIsInstance(formatted, str)
        self.assertIn("检测结果", formatted)
        self.assertIn("car", formatted)
        self.assertIn("person", formatted)


class TestPerformanceComponents(unittest.TestCase):
    """性能组件测试"""
    
    def setUp(self):
        """测试初始化"""
        self.cache = PerceptionCache(max_size=10, ttl=60)
        self.rate_limiter = RateLimiter(max_requests=5, time_window=60)
        self.monitor = PerformanceMonitor(window_size=100)
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        # 测试缓存设置和获取
        key = self.cache.generate_cache_key("check_image", {"image_url": "test.jpg"})
        test_value = {"result": "test"}
        
        # 设置缓存
        self.cache.set(key, test_value)
        
        # 获取缓存
        cached_value = self.cache.get(key)
        self.assertEqual(cached_value, test_value)
        
        # 测试缓存统计
        stats = self.cache.get_stats()
        self.assertEqual(stats["total_entries"], 1)
        self.assertEqual(stats["valid_entries"], 1)
    
    def test_rate_limiter_functionality(self):
        """测试限流器功能"""
        client_id = "test_client"
        
        # 测试允许的请求
        for i in range(5):
            self.assertTrue(self.rate_limiter.is_allowed(client_id))
            self.rate_limiter.record_request(client_id)
        
        # 测试超出限制的请求
        self.assertFalse(self.rate_limiter.is_allowed(client_id))
        
        # 测试剩余请求数
        remaining = self.rate_limiter.get_remaining_requests(client_id)
        self.assertEqual(remaining, 0)
    
    def test_performance_monitor_functionality(self):
        """测试性能监控功能"""
        # 记录一些请求
        self.monitor.record_request(0.1, True, "check_image")
        self.monitor.record_request(0.2, True, "split_image")
        self.monitor.record_request(0.3, False, "props_describe")
        
        # 记录缓存命中
        self.monitor.record_cache_hit()
        self.monitor.record_cache_miss()
        
        # 获取指标
        metrics = self.monitor.get_current_metrics()
        
        self.assertEqual(metrics["total_requests"], 3)
        self.assertEqual(metrics["total_errors"], 1)
        self.assertAlmostEqual(metrics["average_response_time"], 0.2, places=1)
        self.assertEqual(metrics["cache_hits"], 1)
        self.assertEqual(metrics["cache_misses"], 1)
        self.assertEqual(metrics["function_calls"]["check_image"], 1)
        self.assertEqual(metrics["function_calls"]["split_image"], 1)
        self.assertEqual(metrics["function_calls"]["props_describe"], 1)


class TestEndToEndWorkflow(unittest.TestCase):
    """端到端工作流测试"""
    
    def setUp(self):
        """测试初始化"""
        self.logger = Mock()
        self.perception_handler = PerceptionHandler(self.logger)
        self.function_call_manager = FunctionCallManager(self.logger)
    
    def test_complete_function_calling_workflow(self):
        """测试完整的函数调用工作流"""
        # 1. 用户请求
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "请检测这张图片中的汽车"},
                    {"type": "image_url", "image_url": {"url": "https://example.com/car.jpg"}}
                ]
            }
        ]
        
        # 2. 函数调用决策
        decision = self.function_call_manager.should_call_function(messages, function_call="auto")
        
        self.assertTrue(decision.should_call)
        self.assertEqual(decision.function_name, "check_image")
        
        # 3. 参数验证
        is_valid, msg = self.perception_handler.validate_function_call(
            decision.function_name, decision.arguments
        )
        
        self.assertTrue(is_valid)
        
        # 4. 结果格式化（模拟结果）
        mock_result = {
            "taskStatus": "DONE",
            "taskResult": {"labels": ["car"], "scores": [0.95]}
        }
        
        formatted_result = self.perception_handler.format_function_result(
            mock_result, decision.function_name, "检测汽车"
        )
        
        self.assertIsInstance(formatted_result, str)
        self.assertIn("检测结果", formatted_result)
    
    def test_multi_function_selection(self):
        """测试多种函数选择场景"""
        test_cases = [
            ("请分割这张图片", "split_image"),
            ("描述一下这个对象的属性", "props_describe"),
            ("这个物体的角度是多少", "angle_prediction"),
            ("找到关键点位置", "key_point_prediction"),
            ("机器人应该从哪里抓取", "grab_point_prediction"),
            ("对这张图片进行全面分析", "full_perception"),
            ("检测图片中的物体", "check_image"),
        ]
        
        for text, expected_function in test_cases:
            with self.subTest(text=text):
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": text},
                            {"type": "image_url", "image_url": {"url": "https://example.com/image.jpg"}}
                        ]
                    }
                ]
                
                decision = self.function_call_manager.should_call_function(messages, function_call="auto")
                
                self.assertTrue(decision.should_call)
                self.assertEqual(decision.function_name, expected_function)


def run_integration_tests():
    """运行集成测试"""
    print("🧪 开始运行简化集成测试...")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestComponentIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestPerformanceComponents))
    suite.addTests(loader.loadTestsFromTestCase(TestEndToEndWorkflow))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✅ 所有集成测试通过！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
        
        # 显示性能统计
        print("\n📊 测试性能统计:")
        print(f"   - 组件集成测试: 通过")
        print(f"   - 性能组件测试: 通过") 
        print(f"   - 端到端工作流测试: 通过")
        
    else:
        print("❌ 集成测试失败！")
        print(f"   运行测试: {result.testsRun}")
        print(f"   失败: {len(result.failures)}")
        print(f"   错误: {len(result.errors)}")
        
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
