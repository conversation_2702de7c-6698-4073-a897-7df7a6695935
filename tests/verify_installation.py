#!/usr/bin/env python3
"""
验证 py-qj-robots 包的安装和基本功能
"""

import sys
import importlib.util

def check_package_installed():
    """检查包是否已安装"""
    try:
        import py_qj_robots
        print(f"✓ py-qj-robots 包已安装")
        return True
    except ImportError:
        print("✗ py-qj-robots 包未安装")
        print("请运行: pip install py-qj-robots")
        return False

def check_basic_import():
    """检查基本导入功能"""
    try:
        from py_qj_robots import Perception
        print("✓ 基本导入功能正常")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def check_sync_mode():
    """检查同步模式初始化"""
    try:
        from py_qj_robots import Perception
        # 不设置环境变量，只测试初始化逻辑
        print("✓ 同步模式类定义正常")
        return True
    except Exception as e:
        print(f"✗ 同步模式检查失败: {e}")
        return False

def check_async_mode():
    """检查异步模式初始化"""
    try:
        from py_qj_robots import Perception
        # 测试异步模式参数
        print("✓ 异步模式类定义正常")
        return True
    except Exception as e:
        print(f"✗ 异步模式检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    dependencies = [
        ('requests', 'requests'),
        ('python-dotenv', 'dotenv')
    ]
    all_ok = True

    for display_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✓ 依赖包 {display_name} 已安装")
        except ImportError:
            print(f"✗ 依赖包 {display_name} 未安装")
            all_ok = False

    return all_ok

def main():
    """主验证函数"""
    print("=== py-qj-robots 包安装验证 ===\n")
    
    checks = [
        ("包安装检查", check_package_installed),
        ("基本导入检查", check_basic_import),
        ("同步模式检查", check_sync_mode),
        ("异步模式检查", check_async_mode),
        ("依赖包检查", check_dependencies),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"执行 {name}...")
        result = check_func()
        results.append(result)
        print()
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print("=== 验证结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有检查都通过了！包安装正常。")
        print("\n下一步:")
        print("1. 设置环境变量 QJ_APP_ID 和 QJ_APP_SECRET")
        print("2. 运行 python async_usage_example.py 查看使用示例")
        print("3. 运行 python high_performance_test.py 进行性能测试")
        return 0
    else:
        print("❌ 部分检查失败，请检查安装。")
        return 1

if __name__ == '__main__':
    sys.exit(main())
