#!/usr/bin/env python3
"""
Test script for token authentication in the OpenAI-compatible API.

This script tests the token authentication system without requiring
actual API calls to QJ Robots services.
"""

import json
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.token_config import TokenManager


def test_token_generation():
    """Test token generation and management"""
    print("=== Testing Token Generation ===")
    
    try:
        manager = TokenManager("test_tokens.json")
        
        # Add a test token
        token = manager.add_token(
            app_id="test_app_id",
            app_secret="test_app_secret", 
            description="Test token"
        )
        
        print(f"✅ Token generated: {token}")
        print(f"   Length: {len(token)} characters")
        print(f"   Starts with 'sk-': {token.startswith('sk-')}")
        
        # Validate token
        is_valid = manager.validate_token(token)
        print(f"✅ Token validation: {is_valid}")
        
        # Get credentials
        creds = manager.get_credentials(token)
        print(f"✅ Credentials retrieved: {creds is not None}")
        if creds:
            print(f"   App ID: {creds[0]}")
            print(f"   App Secret: {creds[1][:10]}...")
        
        # Clean up
        if os.path.exists("test_tokens.json"):
            os.remove("test_tokens.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Token generation test failed: {e}")
        return False


def test_token_mapping():
    """Test token mapping functionality"""
    print("\n=== Testing Token Mapping ===")
    
    try:
        manager = TokenManager("test_mapping.json")
        
        # Add multiple tokens
        tokens = []
        for i in range(3):
            token = manager.add_token(
                app_id=f"app_id_{i}",
                app_secret=f"app_secret_{i}",
                description=f"Test token {i}"
            )
            tokens.append(token)
        
        print(f"✅ Created {len(tokens)} tokens")
        
        # Test listing
        token_list = manager.list_tokens()
        print(f"✅ Listed {len(token_list)} tokens")
        
        # Test validation of each token
        for i, token in enumerate(tokens):
            creds = manager.get_credentials(token)
            if creds and creds[0] == f"app_id_{i}":
                print(f"✅ Token {i} credentials correct")
            else:
                print(f"❌ Token {i} credentials incorrect")
                return False
        
        # Test invalid token
        invalid_creds = manager.get_credentials("sk-invalid-token")
        if invalid_creds is None:
            print("✅ Invalid token correctly rejected")
        else:
            print("❌ Invalid token incorrectly accepted")
            return False
        
        # Clean up
        if os.path.exists("test_mapping.json"):
            os.remove("test_mapping.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Token mapping test failed: {e}")
        return False


def test_server_integration():
    """Test server integration (mock)"""
    print("\n=== Testing Server Integration (Mock) ===")
    
    try:
        # Mock the server authentication logic
        manager = TokenManager("test_server.json")
        
        # Add a test token
        token = manager.add_token(
            app_id="server_test_app",
            app_secret="server_test_secret",
            description="Server integration test"
        )
        
        print(f"✅ Test token created: {token[:20]}...")
        
        # Simulate server authentication
        def authenticate_request(auth_header):
            if not auth_header or not auth_header.startswith("Bearer "):
                return None, "Missing or invalid authorization header"
            
            token = auth_header[7:]  # Remove "Bearer " prefix
            creds = manager.get_credentials(token)
            
            if not creds:
                return None, "Invalid token"
            
            return creds, "Authentication successful"
        
        # Test valid authentication
        auth_header = f"Bearer {token}"
        creds, message = authenticate_request(auth_header)
        
        if creds:
            print(f"✅ Authentication successful: {message}")
            print(f"   App ID: {creds[0]}")
        else:
            print(f"❌ Authentication failed: {message}")
            return False
        
        # Test invalid authentication
        creds, message = authenticate_request("Bearer sk-invalid-token")
        if not creds:
            print(f"✅ Invalid token rejected: {message}")
        else:
            print(f"❌ Invalid token accepted")
            return False
        
        # Test missing header
        creds, message = authenticate_request("")
        if not creds:
            print(f"✅ Missing header rejected: {message}")
        else:
            print(f"❌ Missing header accepted")
            return False
        
        # Clean up
        if os.path.exists("test_server.json"):
            os.remove("test_server.json")
        
        return True
        
    except Exception as e:
        print(f"❌ Server integration test failed: {e}")
        return False


def test_openai_compatibility():
    """Test OpenAI compatibility format"""
    print("\n=== Testing OpenAI Compatibility ===")
    
    try:
        manager = TokenManager("test_openai.json")
        
        # Generate tokens in OpenAI format
        tokens = []
        for i in range(5):
            token = manager.generate_token()
            tokens.append(token)
        
        print(f"✅ Generated {len(tokens)} OpenAI-format tokens")
        
        # Verify format
        for i, token in enumerate(tokens):
            if not token.startswith("sk-"):
                print(f"❌ Token {i} doesn't start with 'sk-'")
                return False
            
            if len(token) != 51:  # OpenAI tokens are typically 51 chars
                print(f"❌ Token {i} length is {len(token)}, expected 51")
                return False
        
        print("✅ All tokens match OpenAI format")
        
        # Test uniqueness
        unique_tokens = set(tokens)
        if len(unique_tokens) == len(tokens):
            print("✅ All tokens are unique")
        else:
            print(f"❌ Found duplicate tokens: {len(tokens)} generated, {len(unique_tokens)} unique")
            return False
        
        # Clean up
        if os.path.exists("test_openai.json"):
            os.remove("test_openai.json")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI compatibility test failed: {e}")
        return False


def show_generated_tokens():
    """Show the actual generated tokens for manual testing"""
    print("\n=== Generated Tokens for Manual Testing ===")
    
    try:
        # Load the actual token mapping
        manager = TokenManager()
        tokens = manager.list_tokens()
        
        if not tokens:
            print("No tokens found. Run 'python token_config.py' to generate tokens.")
            return
        
        print("Available tokens (replace YOUR_APP_ID_X and YOUR_APP_SECRET_X with actual values):")
        print("-" * 80)
        
        for token, info in tokens.items():
            print(f"Token: {token}")
            print(f"  App ID: {info['app_id']}")
            print(f"  Description: {info['description']}")
            print(f"  Status: {'✅ Ready' if not info['app_id'].startswith('YOUR_APP_ID') else '⚠️  Needs configuration'}")
            print()
        
        print("To use these tokens:")
        print("1. Edit token_mapping.json")
        print("2. Replace YOUR_APP_ID_X with your actual QJ Robots App ID")
        print("3. Replace YOUR_APP_SECRET_X with your actual QJ Robots App Secret")
        print("4. Use the token in your OpenAI client:")
        print("   openai.api_key = 'sk-your-token-here'")
        
    except Exception as e:
        print(f"Error loading tokens: {e}")


def main():
    """Run all tests"""
    print("QJ Robots Token Authentication Test Suite")
    print("=" * 50)
    
    tests = [
        test_token_generation,
        test_token_mapping,
        test_server_integration,
        test_openai_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All token authentication tests passed!")
        show_generated_tokens()
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
