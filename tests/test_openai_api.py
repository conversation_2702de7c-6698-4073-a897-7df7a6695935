#!/usr/bin/env python3
"""
Test script for the OpenAI-compatible QJ Robots Perception API.

This script tests the API functionality without requiring actual API credentials,
focusing on the OpenAI compatibility layer and response formatting.
"""

import json
import sys
import os

# Add the parent directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_api_structure():
    """Test the API structure and imports"""
    print("=== Testing API Structure ===")
    
    try:
        from py_qj_robots.openai_api import (
            OpenAIPerceptionAPI, 
            Message, 
            Choice, 
            Usage, 
            ChatCompletion,
            ChatCompletionChunk,
            PerceptionFunction
        )
        print("✓ All imports successful")
        
        # Test dataclass creation
        message = Message(role="user", content="test")
        print(f"✓ Message created: {message.role}")
        
        usage = Usage(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        print(f"✓ Usage created: {usage.total_tokens} tokens")
        
        choice = Choice(index=0, message=message)
        print(f"✓ Choice created: index {choice.index}")
        
        completion = ChatCompletion(id="test-123", choices=[choice], usage=usage)
        print(f"✓ ChatCompletion created: {completion.id}")
        
        chunk = ChatCompletionChunk(id="test-chunk-123")
        print(f"✓ ChatCompletionChunk created: {chunk.id}")
        
        func = PerceptionFunction("test_func", "Test function", {"type": "object"})
        print(f"✓ PerceptionFunction created: {func.name}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import/structure test failed: {e}")
        return False


def test_function_definitions():
    """Test function definitions without API calls"""
    print("\n=== Testing Function Definitions ===")
    
    try:
        # Mock the Perception class to avoid API calls
        class MockPerception:
            def __init__(self, **kwargs):
                self.enable_async_mode = kwargs.get('enable_async_mode', False)
        
        # Temporarily replace the import
        import py_qj_robots.openai_api as openai_api
        original_perception = openai_api.Perception
        openai_api.Perception = MockPerception
        
        try:
            api = openai_api.OpenAIPerceptionAPI()
            print("✓ API initialized with mock")
            
            # Test function listing
            functions = api.list_functions()
            print(f"✓ Found {len(functions)} functions:")
            
            for func in functions:
                func_info = func["function"]
                print(f"  - {func_info['name']}: {func_info['description']}")
            
            # Verify expected functions are present
            expected_functions = [
                "check_image", "split_image", "props_describe", 
                "angle_prediction", "key_point_prediction", 
                "grab_point_prediction", "full_perception"
            ]
            
            actual_functions = [f["function"]["name"] for f in functions]
            for expected in expected_functions:
                if expected in actual_functions:
                    print(f"  ✓ {expected} found")
                else:
                    print(f"  ✗ {expected} missing")
            
            return True
            
        finally:
            # Restore original
            openai_api.Perception = original_perception
            
    except Exception as e:
        print(f"✗ Function definition test failed: {e}")
        return False


def test_response_formatting():
    """Test response formatting methods"""
    print("\n=== Testing Response Formatting ===")
    
    try:
        # Mock the Perception class
        class MockPerception:
            def __init__(self, **kwargs):
                self.enable_async_mode = kwargs.get('enable_async_mode', False)
        
        import py_qj_robots.openai_api as openai_api
        original_perception = openai_api.Perception
        openai_api.Perception = MockPerception
        
        try:
            api = openai_api.OpenAIPerceptionAPI()
            
            # Test helpful response
            response = api._create_helpful_response("Hello", api.list_functions())
            print("✓ Helpful response generated")
            print(f"  Preview: {response[:100]}...")
            
            # Test result formatting with mock data
            mock_results = [
                {
                    "success": True,
                    "function_name": "check_image",
                    "result": {
                        "taskResult": {
                            "labels": ["apple", "banana"],
                            "scores": [0.95, 0.87],
                            "boxes": [[10, 20, 100, 120], [150, 30, 200, 140]]
                        }
                    }
                },
                {
                    "success": True,
                    "function_name": "split_image",
                    "result": {
                        "taskResult": {
                            "labels": ["cup", "plate"],
                            "masks": [{"maskImage": "url1"}, {"maskImage": "url2"}],
                            "croppedImagesListBbox": ["crop1.jpg", "crop2.jpg"]
                        }
                    }
                }
            ]
            
            for mock_result in mock_results:
                formatted = api._format_perception_result(mock_result)
                print(f"✓ {mock_result['function_name']} result formatted")
                print(f"  Preview: {formatted[:100]}...")
            
            return True
            
        finally:
            openai_api.Perception = original_perception
            
    except Exception as e:
        print(f"✗ Response formatting test failed: {e}")
        return False


def test_completion_creation():
    """Test completion response creation"""
    print("\n=== Testing Completion Creation ===")
    
    try:
        # Mock the Perception class
        class MockPerception:
            def __init__(self, **kwargs):
                self.enable_async_mode = kwargs.get('enable_async_mode', False)
        
        import py_qj_robots.openai_api as openai_api
        original_perception = openai_api.Perception
        openai_api.Perception = MockPerception
        
        try:
            api = openai_api.OpenAIPerceptionAPI()
            
            # Test completion response creation
            completion = api._create_completion_response(
                "test-123", 
                "This is a test response", 
                10, 
                5
            )
            
            print("✓ Completion response created")
            print(f"  ID: {completion.id}")
            print(f"  Model: {completion.model}")
            print(f"  Content: {completion.choices[0].message.content}")
            print(f"  Usage: {completion.usage.total_tokens} tokens")
            
            # Test streaming response creation
            stream = api._create_streaming_response("test-stream-123", "Hello world", 5, 2)
            chunks = list(stream)
            
            print(f"✓ Streaming response created with {len(chunks)} chunks")
            print(f"  First chunk: {chunks[0].id}")
            print(f"  Last chunk finish reason: {chunks[-1].choices[0]['finish_reason']}")
            
            return True
            
        finally:
            openai_api.Perception = original_perception
            
    except Exception as e:
        print(f"✗ Completion creation test failed: {e}")
        return False


def test_json_parsing():
    """Test JSON parsing for function calls"""
    print("\n=== Testing JSON Parsing ===")
    
    try:
        # Mock the Perception class
        class MockPerception:
            def __init__(self, **kwargs):
                self.enable_async_mode = kwargs.get('enable_async_mode', False)
        
        import py_qj_robots.openai_api as openai_api
        original_perception = openai_api.Perception
        openai_api.Perception = MockPerception
        
        try:
            api = openai_api.OpenAIPerceptionAPI()
            
            # Test valid JSON parsing
            valid_json = '{"function": "check_image", "arguments": {"image_type": "2D", "image_url": "test.jpg", "object_names": ["apple"]}}'
            parsed = api._parse_function_call(valid_json)
            print("✓ Valid JSON parsed successfully")
            print(f"  Function: {parsed['function']}")
            
            # Test invalid JSON
            invalid_json = "This is not JSON"
            parsed = api._parse_function_call(invalid_json)
            print(f"✓ Invalid JSON handled: {parsed is None}")
            
            # Test function call with prefix
            prefixed_json = f"function_call:\n{valid_json}"
            parsed = api._parse_function_call(prefixed_json)
            print(f"✓ Prefixed JSON parsed: {parsed is not None}")
            
            return True
            
        finally:
            openai_api.Perception = original_perception
            
    except Exception as e:
        print(f"✗ JSON parsing test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("QJ Robots OpenAI API Test Suite")
    print("=" * 40)
    
    tests = [
        test_api_structure,
        test_function_definitions,
        test_response_formatting,
        test_completion_creation,
        test_json_parsing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The OpenAI API is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
