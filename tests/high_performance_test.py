import os
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import defaultdict
import statistics

from dotenv import load_dotenv
from py_qj_robots import Perception


def init_perception_async():
    """Initialize Perception with async mode enabled"""
    load_dotenv()
    
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')
    
    # Enable async mode with more workers
    return Perception(enable_async_mode=True, max_workers=20)


def init_perception_sync():
    """Initialize Perception with sync mode"""
    load_dotenv()
    
    required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')
    
    return Perception()


class PerformanceTest:
    def __init__(self):
        self.results = []
        self.lock = threading.Lock()
        self.completed_tasks = 0
        self.failed_tasks = 0
        
    def callback_handler(self, result, error):
        """Callback for async tasks"""
        with self.lock:
            if error:
                self.failed_tasks += 1
                print(f"Task failed: {error}")
            else:
                self.completed_tasks += 1
                print(f"Task completed: {self.completed_tasks}")
    
    def test_async_mode(self, total_requests=100, concurrent_limit=50):
        """Test async mode performance"""
        print(f"\n=== 异步模式测试: {total_requests} 请求, {concurrent_limit} 并发 ===")
        
        perception = init_perception_async()
        image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
        object_names = ["bottle", "cup"]
        
        start_time = time.time()
        task_ids = []
        
        # Submit all tasks quickly
        submit_start = time.time()
        for i in range(total_requests):
            try:
                task_id = perception.check_image_async(
                    image_type="2D",
                    image_url=image_url,
                    object_names=object_names,
                    callback=self.callback_handler,
                    timeout=30
                )
                task_ids.append(task_id)
            except Exception as e:
                print(f"Failed to submit task {i}: {e}")
        
        submit_time = time.time() - submit_start
        print(f"任务提交完成: {len(task_ids)} 个任务, 耗时: {submit_time:.2f} 秒")
        print(f"提交QPS: {len(task_ids) / submit_time:.2f}")
        
        # Wait for all tasks to complete
        print("等待所有任务完成...")
        while self.completed_tasks + self.failed_tasks < len(task_ids):
            time.sleep(0.1)
        
        total_time = time.time() - start_time
        
        print(f"\n异步模式测试结果:")
        print(f"总请求数: {total_requests}")
        print(f"成功数: {self.completed_tasks}")
        print(f"失败数: {self.failed_tasks}")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"整体QPS: {total_requests / total_time:.2f}")
        print(f"提交QPS: {len(task_ids) / submit_time:.2f}")
        
        return {
            'mode': 'async',
            'total_requests': total_requests,
            'success_count': self.completed_tasks,
            'failed_count': self.failed_tasks,
            'total_time': total_time,
            'submit_time': submit_time,
            'overall_qps': total_requests / total_time,
            'submit_qps': len(task_ids) / submit_time
        }
    
    def test_sync_mode(self, total_requests=20, concurrent_threads=10):
        """Test sync mode performance"""
        print(f"\n=== 同步模式测试: {total_requests} 请求, {concurrent_threads} 线程 ===")
        
        perception = init_perception_sync()
        image_url = "http://gips3.baidu.com/it/u=1821127123,1149655687&fm=3028&app=3028&f=JPEG&fmt=auto?w=720&h=1280"
        object_names = ["bottle", "cup"]
        
        results = []
        request_counter = 0
        lock = threading.Lock()
        
        def worker():
            nonlocal request_counter
            local_results = []
            
            while True:
                with lock:
                    if request_counter >= total_requests:
                        results.extend(local_results)
                        return
                    request_counter += 1
                    current_request = request_counter
                
                start_time = time.time()
                try:
                    result = perception.check_image(
                        image_type="2D",
                        image_url=image_url,
                        object_names=object_names
                    )
                    elapsed_time = time.time() - start_time
                    local_results.append({
                        'success': True,
                        'elapsed_time': elapsed_time,
                        'request_id': current_request
                    })
                    print(f"同步请求 {current_request} 完成, 耗时: {elapsed_time:.2f}s")
                except Exception as e:
                    elapsed_time = time.time() - start_time
                    local_results.append({
                        'success': False,
                        'elapsed_time': elapsed_time,
                        'request_id': current_request,
                        'error': str(e)
                    })
                    print(f"同步请求 {current_request} 失败: {e}")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_threads) as executor:
            futures = [executor.submit(worker) for _ in range(concurrent_threads)]
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"Worker thread error: {e}")
        
        total_time = time.time() - start_time
        
        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count
        avg_response_time = statistics.mean([r['elapsed_time'] for r in results]) if results else 0
        
        print(f"\n同步模式测试结果:")
        print(f"总请求数: {total_requests}")
        print(f"成功数: {success_count}")
        print(f"失败数: {failed_count}")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"QPS: {total_requests / total_time:.2f}")
        print(f"平均响应时间: {avg_response_time:.2f} 秒")
        
        return {
            'mode': 'sync',
            'total_requests': total_requests,
            'success_count': success_count,
            'failed_count': failed_count,
            'total_time': total_time,
            'qps': total_requests / total_time,
            'avg_response_time': avg_response_time
        }


def main():
    """Main test function"""
    test = PerformanceTest()
    
    # Test sync mode first (smaller scale)
    sync_result = test.test_sync_mode(total_requests=20, concurrent_threads=10)
    
    # Reset counters for async test
    test.completed_tasks = 0
    test.failed_tasks = 0
    
    # Test async mode (larger scale)
    async_result = test.test_async_mode(total_requests=100, concurrent_limit=50)
    
    # Compare results
    print(f"\n=== 性能对比 ===")
    print(f"同步模式 QPS: {sync_result['qps']:.2f}")
    print(f"异步模式 整体QPS: {async_result['overall_qps']:.2f}")
    print(f"异步模式 提交QPS: {async_result['submit_qps']:.2f}")
    print(f"性能提升: {async_result['submit_qps'] / sync_result['qps']:.2f}x")

    print(f"\n=== 结论 ===")
    print(f"异步模式通过分离任务提交和结果轮询，实现了显著的性能提升：")
    print(f"- 提交阶段QPS提升了 {async_result['submit_qps'] / sync_result['qps']:.1f} 倍")
    print(f"- 可以快速提交大量任务，后台异步处理结果")
    print(f"- 更好地利用了服务器的并发处理能力")


if __name__ == '__main__':
    main()
