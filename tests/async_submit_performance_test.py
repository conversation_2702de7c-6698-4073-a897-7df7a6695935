#!/usr/bin/env python3
"""
异步提交并发性能测试脚本

专门测试异步提交的并发性能，只关注任务提交阶段：
- 获取到taskId就表示成功
- 不等待任务完成，不处理结果
- 专注测试服务器接收任务的并发能力
"""

import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import statistics
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple

from dotenv import load_dotenv
from py_qj_robots import Perception


@dataclass
class SubmitPerformanceResult:
    """异步提交性能测试结果"""
    test_name: str
    concurrency_level: int
    total_requests: int
    successful_submits: int
    failed_submits: int
    
    # 时间指标
    test_duration: float
    submit_qps: float
    
    # 提交延迟指标
    avg_submit_latency: float
    p50_submit_latency: float
    p95_submit_latency: float
    p99_submit_latency: float
    min_submit_latency: float
    max_submit_latency: float
    
    # 错误统计
    error_types: Dict[str, int]
    success_rate: float
    
    # 任务ID列表（用于后续验证）
    task_ids: List[str]


class AsyncSubmitTester:
    """异步提交性能测试器"""
    
    def __init__(self):
        self.results_lock = threading.Lock()
        self.submit_records = []  # 存储每次提交的记录
        
    def init_perception(self, max_workers: int) -> Perception:
        """初始化Perception实例 - M1 Pro优化版"""
        load_dotenv()

        required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')

        print(f"🔧 初始化Perception: max_workers={max_workers}")
        return Perception(enable_async_mode=True, max_workers=max_workers)

    def init_optimized_perception(self, max_workers: int) -> Perception:
        """初始化优化的Perception实例，专门为M1 Pro调优"""
        import requests
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        load_dotenv()

        required_vars = ['QJ_APP_ID', 'QJ_APP_SECRET']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(f'缺少必要的环境变量: {", ".join(missing_vars)}')

        # 创建优化的Perception实例
        perception = Perception(enable_async_mode=True, max_workers=max_workers)

        # 如果有session属性，进行优化配置
        if hasattr(perception, 'session') or hasattr(perception, '_session'):
            session = getattr(perception, 'session', getattr(perception, '_session', None))
            if session:
                # M1 Pro优化的HTTP配置
                retry_strategy = Retry(
                    total=2,  # 减少重试次数，M1 Pro处理速度快
                    backoff_factor=0.1,  # 快速重试
                    status_forcelist=[429, 500, 502, 503, 504],
                )

                adapter = HTTPAdapter(
                    pool_connections=min(100, max_workers * 2),
                    pool_maxsize=min(100, max_workers * 2),
                    max_retries=retry_strategy,
                    pool_block=False  # 非阻塞连接池
                )

                session.mount("http://", adapter)
                session.mount("https://", adapter)

                # 优化超时设置
                session.timeout = (2, 8)  # 快速超时，适合高速网络

                # 启用连接复用
                session.headers.update({
                    'Connection': 'keep-alive',
                    'Keep-Alive': 'timeout=30, max=100'
                })

        print(f"🚀 M1 Pro优化Perception初始化完成: max_workers={max_workers}")
        return perception
    
    def test_burst_submit(self, concurrency_levels: List[int],
                         requests_per_level: int = 100) -> List[SubmitPerformanceResult]:
        """
        突发提交测试 - M1 Pro优化版

        Args:
            concurrency_levels: 并发级别列表，如 [50, 100, 150, 200]
            requests_per_level: 每个并发级别的请求数
        """
        results = []

        for concurrency in concurrency_levels:
            print(f"\n=== M1 Pro突发提交测试: {concurrency} 并发, {requests_per_level} 请求 ===")

            # 重置记录
            self.submit_records.clear()

            # 使用优化的Perception实例
            max_workers = min(concurrency, 96)  # M1 Pro最大96线程
            perception = self.init_optimized_perception(max_workers=max_workers)

            image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
            object_names = ["bottle", "cup"]
            
            test_start_time = time.time()
            
            def submit_single_request(request_id: int) -> Dict:
                """提交单个请求并记录性能"""
                submit_start = time.time()
                
                try:
                    # 异步提交，不设置callback，不等待结果
                    task_id = perception.check_image_async(
                        image_type="2D",
                        image_url=image_url,
                        object_names=object_names,
                        callback=None,  # 不设置回调，不处理结果
                        timeout=120
                    )
                    
                    submit_end = time.time()
                    submit_latency = submit_end - submit_start
                    
                    record = {
                        'request_id': request_id,
                        'submit_start': submit_start,
                        'submit_end': submit_end,
                        'submit_latency': submit_latency,
                        'task_id': task_id,
                        'success': True,
                        'error': None
                    }
                    
                    print(f"✅ 请求 {request_id} 提交成功: {task_id}, 延迟: {submit_latency:.3f}s")
                    
                except Exception as e:
                    submit_end = time.time()
                    submit_latency = submit_end - submit_start
                    
                    record = {
                        'request_id': request_id,
                        'submit_start': submit_start,
                        'submit_end': submit_end,
                        'submit_latency': submit_latency,
                        'task_id': None,
                        'success': False,
                        'error': str(e)
                    }
                    
                    print(f"❌ 请求 {request_id} 提交失败: {e}, 延迟: {submit_latency:.3f}s")
                
                return record
            
            # M1 Pro优化的线程池配置
            # 使用更大的线程池以充分利用M1 Pro的性能
            thread_pool_size = min(concurrency * 2, 200)  # 最大200线程

            print(f"🧵 使用线程池: {thread_pool_size} 线程")

            with ThreadPoolExecutor(max_workers=thread_pool_size) as executor:
                # 分批提交以避免瞬时过载
                batch_size = min(50, requests_per_level)

                for batch_start in range(0, requests_per_level, batch_size):
                    batch_end = min(batch_start + batch_size, requests_per_level)
                    batch_futures = []

                    # 提交当前批次
                    for i in range(batch_start, batch_end):
                        future = executor.submit(submit_single_request, i)
                        batch_futures.append(future)

                    # 收集当前批次结果
                    for future in as_completed(batch_futures):
                        record = future.result()
                        with self.results_lock:
                            self.submit_records.append(record)

                    # 批次间短暂间隔，避免过载
                    if batch_end < requests_per_level:
                        time.sleep(0.05)
            
            test_end_time = time.time()
            
            # 分析结果
            result = self._analyze_submit_results(
                test_name=f"突发提交_{concurrency}",
                concurrency_level=concurrency,
                total_requests=requests_per_level,
                test_start_time=test_start_time,
                test_end_time=test_end_time
            )
            
            results.append(result)
            self._print_submit_result(result)
            
            # 测试间隔，让服务器恢复
            time.sleep(3)
        
        return results
    
    def test_sustained_submit(self, concurrency: int, duration_seconds: int = 300) -> SubmitPerformanceResult:
        """
        持续提交测试 - 测试服务器长时间处理提交请求的能力
        
        Args:
            concurrency: 并发级别
            duration_seconds: 测试持续时间（秒）
        """
        print(f"\n=== 持续提交测试: {concurrency} 并发, {duration_seconds}s 持续时间 ===")
        
        self.submit_records.clear()
        perception = self.init_perception(max_workers=concurrency)
        
        image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
        object_names = ["bottle", "cup"]
        
        test_start_time = time.time()
        request_counter = 0
        stop_flag = threading.Event()
        
        def continuous_submitter(worker_id: int):
            """持续提交请求的工作线程"""
            nonlocal request_counter
            
            while not stop_flag.is_set():
                try:
                    submit_start = time.time()
                    
                    # 异步提交，不处理结果
                    task_id = perception.check_image_async(
                        image_type="2D",
                        image_url=image_url,
                        object_names=object_names,
                        callback=None,
                        timeout=120
                    )
                    
                    submit_end = time.time()
                    submit_latency = submit_end - submit_start
                    
                    with self.results_lock:
                        record = {
                            'request_id': request_counter,
                            'worker_id': worker_id,
                            'submit_start': submit_start,
                            'submit_end': submit_end,
                            'submit_latency': submit_latency,
                            'task_id': task_id,
                            'success': True,
                            'error': None
                        }
                        self.submit_records.append(record)
                        request_counter += 1
                    
                    if request_counter % 10 == 0:
                        print(f"📤 已提交 {request_counter} 个请求")
                    
                    # 控制提交速率，避免过快提交
                    time.sleep(0.05)
                    
                except Exception as e:
                    submit_end = time.time()
                    submit_latency = submit_end - submit_start
                    
                    with self.results_lock:
                        record = {
                            'request_id': request_counter,
                            'worker_id': worker_id,
                            'submit_start': submit_start,
                            'submit_end': submit_end,
                            'submit_latency': submit_latency,
                            'task_id': None,
                            'success': False,
                            'error': str(e)
                        }
                        self.submit_records.append(record)
                        request_counter += 1
                    
                    print(f"❌ 提交失败: {e}")
                    time.sleep(1)  # 失败后等待更长时间
        
        # 启动多个提交线程
        submit_threads = []
        for i in range(min(concurrency, 20)):  # 最多20个提交线程
            thread = threading.Thread(target=continuous_submitter, args=(i,))
            thread.daemon = True
            thread.start()
            submit_threads.append(thread)
        
        # 等待测试时间结束
        time.sleep(duration_seconds)
        stop_flag.set()
        
        # 等待所有提交线程结束
        for thread in submit_threads:
            thread.join(timeout=5)
        
        test_end_time = time.time()
        
        print(f"📤 持续提交测试结束: 共提交 {len(self.submit_records)} 个请求")
        
        # 分析结果
        result = self._analyze_submit_results(
            test_name=f"持续提交_{concurrency}",
            concurrency_level=concurrency,
            total_requests=len(self.submit_records),
            test_start_time=test_start_time,
            test_end_time=test_end_time
        )
        
        self._print_submit_result(result)
        return result

    def test_m1_pro_extreme_performance(self) -> List[SubmitPerformanceResult]:
        """M1 Pro极限性能测试"""
        print(f"\n{'='*80}")
        print("🚀 M1 Pro极限性能测试")
        print("🎯 目标: 测试M1 Pro硬件的绝对性能极限")
        print(f"{'='*80}")

        # M1 Pro极限配置
        extreme_configs = [
            {'concurrency': 100, 'requests': 200, 'workers': 48},
            {'concurrency': 200, 'requests': 300, 'workers': 64},
            {'concurrency': 300, 'requests': 400, 'workers': 80},
            {'concurrency': 400, 'requests': 500, 'workers': 96},
            {'concurrency': 500, 'requests': 600, 'workers': 96},  # 最大线程数限制
        ]

        results = []

        for config in extreme_configs:
            concurrency = config['concurrency']
            requests = config['requests']
            workers = config['workers']

            print(f"\n🔥 极限测试: {concurrency}并发, {requests}请求, {workers}线程")

            try:
                # 重置记录
                self.submit_records.clear()

                # 使用极限配置
                perception = self.init_optimized_perception(max_workers=workers)

                image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
                object_names = ["bottle", "cup"]

                test_start_time = time.time()

                def extreme_submit_request(request_id: int) -> Dict:
                    """极限性能提交请求"""
                    submit_start = time.time()

                    try:
                        task_id = perception.check_image_async(
                            image_type="2D",
                            image_url=image_url,
                            object_names=object_names,
                            callback=None,
                            timeout=60  # 较短超时
                        )

                        submit_end = time.time()
                        submit_latency = submit_end - submit_start

                        if request_id % 50 == 0:  # 每50个请求报告一次
                            print(f"⚡ 请求 {request_id} 完成: {task_id[:20]}..., 延迟: {submit_latency:.3f}s")

                        return {
                            'request_id': request_id,
                            'submit_start': submit_start,
                            'submit_end': submit_end,
                            'submit_latency': submit_latency,
                            'task_id': task_id,
                            'success': True,
                            'error': None
                        }

                    except Exception as e:
                        submit_end = time.time()
                        submit_latency = submit_end - submit_start

                        if request_id % 50 == 0:
                            print(f"💥 请求 {request_id} 失败: {str(e)[:50]}..., 延迟: {submit_latency:.3f}s")

                        return {
                            'request_id': request_id,
                            'submit_start': submit_start,
                            'submit_end': submit_end,
                            'submit_latency': submit_latency,
                            'task_id': None,
                            'success': False,
                            'error': str(e)
                        }

                # 使用最大线程池进行极限测试
                with ThreadPoolExecutor(max_workers=min(concurrency, 300)) as executor:
                    print(f"🧵 启动 {min(concurrency, 300)} 个线程进行极限测试...")

                    futures = [executor.submit(extreme_submit_request, i)
                              for i in range(requests)]

                    # 收集结果
                    completed = 0
                    for future in as_completed(futures):
                        record = future.result()
                        with self.results_lock:
                            self.submit_records.append(record)

                        completed += 1
                        if completed % 100 == 0:
                            print(f"📊 已完成 {completed}/{requests} 请求")

                test_end_time = time.time()

                # 分析极限测试结果
                result = self._analyze_submit_results(
                    test_name=f"M1_Pro_极限_{concurrency}并发",
                    concurrency_level=concurrency,
                    total_requests=requests,
                    test_start_time=test_start_time,
                    test_end_time=test_end_time
                )

                results.append(result)
                self._print_submit_result(result)

                # 极限测试间隔，让系统恢复
                print("😴 系统恢复中...")
                time.sleep(10)

            except Exception as e:
                print(f"💥 极限测试失败 (并发{concurrency}): {e}")
                continue

        return results

    def _analyze_submit_results(self, test_name: str, concurrency_level: int,
                               total_requests: int, test_start_time: float,
                               test_end_time: float) -> SubmitPerformanceResult:
        """分析提交测试结果"""

        with self.results_lock:
            # 统计基本指标
            successful_submits = sum(1 for record in self.submit_records if record['success'])
            failed_submits = total_requests - successful_submits

            # 计算提交延迟
            submit_latencies = [record['submit_latency'] for record in self.submit_records]

            # 统计错误类型
            error_types = defaultdict(int)
            for record in self.submit_records:
                if not record['success'] and record['error']:
                    error_type = record['error'].split(':')[0] if ':' in record['error'] else record['error']
                    error_types[error_type] += 1

            # 计算QPS
            test_duration = test_end_time - test_start_time
            submit_qps = successful_submits / test_duration if test_duration > 0 else 0

            # 计算延迟统计
            if submit_latencies:
                submit_latencies.sort()
                n = len(submit_latencies)
                p50_submit_latency = submit_latencies[int(n * 0.5)]
                p95_submit_latency = submit_latencies[int(n * 0.95)]
                p99_submit_latency = submit_latencies[int(n * 0.99)]
                avg_submit_latency = statistics.mean(submit_latencies)
                min_submit_latency = min(submit_latencies)
                max_submit_latency = max(submit_latencies)
            else:
                p50_submit_latency = p95_submit_latency = p99_submit_latency = 0
                avg_submit_latency = min_submit_latency = max_submit_latency = 0

            # 收集成功的task_ids
            task_ids = [record['task_id'] for record in self.submit_records
                       if record['success'] and record['task_id']]

            success_rate = successful_submits / total_requests if total_requests > 0 else 0

        return SubmitPerformanceResult(
            test_name=test_name,
            concurrency_level=concurrency_level,
            total_requests=total_requests,
            successful_submits=successful_submits,
            failed_submits=failed_submits,
            test_duration=test_duration,
            submit_qps=submit_qps,
            avg_submit_latency=avg_submit_latency,
            p50_submit_latency=p50_submit_latency,
            p95_submit_latency=p95_submit_latency,
            p99_submit_latency=p99_submit_latency,
            min_submit_latency=min_submit_latency,
            max_submit_latency=max_submit_latency,
            error_types=dict(error_types),
            success_rate=success_rate,
            task_ids=task_ids
        )

    def _print_submit_result(self, result: SubmitPerformanceResult):
        """打印提交测试结果"""
        print(f"\n{'='*60}")
        print(f"📊 {result.test_name} 测试结果")
        print(f"{'='*60}")
        print(f"并发级别: {result.concurrency_level}")
        print(f"总请求数: {result.total_requests}")
        print(f"成功提交: {result.successful_submits}")
        print(f"失败提交: {result.failed_submits}")
        print(f"成功率: {result.success_rate*100:.1f}%")
        print(f"")
        print(f"⏱️  性能指标:")
        print(f"  测试时间: {result.test_duration:.2f}s")
        print(f"  提交QPS: {result.submit_qps:.2f}")
        print(f"")
        print(f"📈 提交延迟指标:")
        print(f"  平均延迟: {result.avg_submit_latency:.3f}s")
        print(f"  P50延迟: {result.p50_submit_latency:.3f}s")
        print(f"  P95延迟: {result.p95_submit_latency:.3f}s")
        print(f"  P99延迟: {result.p99_submit_latency:.3f}s")
        print(f"  最小延迟: {result.min_submit_latency:.3f}s")
        print(f"  最大延迟: {result.max_submit_latency:.3f}s")
        print(f"")
        print(f"📋 任务统计:")
        print(f"  获得TaskID数量: {len(result.task_ids)}")

        if result.error_types:
            print(f"")
            print(f"❌ 错误统计:")
            for error_type, count in result.error_types.items():
                print(f"  {error_type}: {count}")

    def save_results_to_csv(self, results: List[SubmitPerformanceResult],
                           filename: str = "async_submit_results.csv"):
        """保存结果到CSV文件"""
        import csv

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'test_name', 'concurrency_level', 'total_requests', 'successful_submits',
                'failed_submits', 'success_rate', 'test_duration', 'submit_qps',
                'avg_submit_latency', 'p50_submit_latency', 'p95_submit_latency',
                'p99_submit_latency', 'min_submit_latency', 'max_submit_latency',
                'task_ids_count'
            ]

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                writer.writerow({
                    'test_name': result.test_name,
                    'concurrency_level': result.concurrency_level,
                    'total_requests': result.total_requests,
                    'successful_submits': result.successful_submits,
                    'failed_submits': result.failed_submits,
                    'success_rate': f"{result.success_rate*100:.1f}%",
                    'test_duration': f"{result.test_duration:.2f}",
                    'submit_qps': f"{result.submit_qps:.2f}",
                    'avg_submit_latency': f"{result.avg_submit_latency:.3f}",
                    'p50_submit_latency': f"{result.p50_submit_latency:.3f}",
                    'p95_submit_latency': f"{result.p95_submit_latency:.3f}",
                    'p99_submit_latency': f"{result.p99_submit_latency:.3f}",
                    'min_submit_latency': f"{result.min_submit_latency:.3f}",
                    'max_submit_latency': f"{result.max_submit_latency:.3f}",
                    'task_ids_count': len(result.task_ids)
                })

        print(f"📄 结果已保存到: {filename}")

    def save_task_ids(self, results: List[SubmitPerformanceResult],
                     filename: str = "submitted_task_ids.txt"):
        """保存所有成功提交的task_ids到文件"""
        all_task_ids = []
        for result in results:
            all_task_ids.extend(result.task_ids)

        with open(filename, 'w', encoding='utf-8') as f:
            for task_id in all_task_ids:
                f.write(f"{task_id}\n")

        print(f"📋 已保存 {len(all_task_ids)} 个TaskID到: {filename}")


def get_optimal_config_for_m1_pro():
    """为M1 Pro优化的配置"""
    import os

    # M1 Pro硬件特征
    cpu_cores = 8  # 6性能核 + 2能效核
    performance_cores = 6
    memory_gb = 16
    network_bandwidth_mbps = 300

    # 基于硬件计算最优配置
    config = {
        # 线程配置 - 基于性能核心数
        'max_workers_conservative': performance_cores * 2,      # 12线程
        'max_workers_balanced': performance_cores * 4,          # 24线程
        'max_workers_aggressive': performance_cores * 8,        # 48线程
        'max_workers_extreme': performance_cores * 16,          # 96线程

        # 并发配置 - 基于网络带宽和内存
        'concurrency_conservative': [10, 20, 30],
        'concurrency_balanced': [20, 40, 60, 80],
        'concurrency_aggressive': [50, 100, 150, 200],
        'concurrency_extreme': [100, 200, 300, 400, 500],

        # 请求配置
        'requests_per_test': 100,  # 增加请求数以更好测试性能

        # 连接池配置
        'connection_pool_size': min(200, performance_cores * 20),

        # 超时配置 - 基于高速网络优化
        'timeout_fast': (1, 5),
        'timeout_normal': (2, 8),
        'timeout_safe': (3, 12)
    }

    print(f"🖥️  硬件配置检测:")
    print(f"   CPU: Apple M1 Pro ({cpu_cores}核)")
    print(f"   性能核心: {performance_cores}个")
    print(f"   内存: {memory_gb}GB")
    print(f"   网络: {network_bandwidth_mbps}Mbps上行")
    print(f"")
    print(f"🎯 优化配置:")
    print(f"   保守线程数: {config['max_workers_conservative']}")
    print(f"   平衡线程数: {config['max_workers_balanced']}")
    print(f"   激进线程数: {config['max_workers_aggressive']}")
    print(f"   极限线程数: {config['max_workers_extreme']}")

    return config


def main():
    """主测试函数 - M1 Pro优化版"""
    # 获取M1 Pro优化配置
    config = get_optimal_config_for_m1_pro()

    tester = AsyncSubmitTester()

    try:
        print("🚀 开始M1 Pro优化的异步提交并发性能测试")
        print("📋 测试目标: 充分利用M1 Pro性能，测试异步提交极限")

        # 测试1: 渐进式并发测试
        print("\n" + "="*80)
        print("📈 M1 Pro渐进式并发测试")
        print("="*80)

        burst_results = tester.test_burst_submit(
            concurrency_levels=config['concurrency_aggressive'],
            requests_per_level=config['requests_per_test']
        )

        # 测试2: M1 Pro极限性能测试
        print("\n" + "="*80)
        print("🔥 M1 Pro极限性能测试")
        print("="*80)

        extreme_results = tester.test_m1_pro_extreme_performance()
        burst_results.extend(extreme_results)

        # 测试3: 持续提交测试（可选）
        # print("\n" + "="*80)
        # print("⏱️  持续提交测试 - 测试服务器长时间处理提交的能力")
        # print("="*80)
        #
        # sustained_result = tester.test_sustained_submit(
        #     concurrency=100,  # M1 Pro可以支持更高并发
        #     duration_seconds=180  # 3分钟测试
        # )
        # burst_results.append(sustained_result)

        # 保存结果
        tester.save_results_to_csv(burst_results, "async_submit_performance.csv")
        tester.save_task_ids(burst_results, "submitted_task_ids.txt")

        # 总结报告
        print(f"\n{'='*80}")
        print("📊 异步提交性能测试总结")
        print(f"{'='*80}")

        for result in burst_results:
            print(f"{result.test_name}: 并发{result.concurrency_level}, "
                  f"提交QPS={result.submit_qps:.2f}, "
                  f"成功率={result.success_rate*100:.1f}%, "
                  f"P95延迟={result.p95_submit_latency:.3f}s, "
                  f"TaskID数量={len(result.task_ids)}")

        # 找出最佳性能点
        best_qps_result = max(burst_results, key=lambda x: x.submit_qps)
        print(f"\n🏆 最佳提交QPS: {best_qps_result.submit_qps:.2f} "
              f"(并发度: {best_qps_result.concurrency_level})")

        # 找出最佳延迟点
        best_latency_result = min(burst_results, key=lambda x: x.p95_submit_latency)
        print(f"🏆 最佳P95延迟: {best_latency_result.p95_submit_latency:.3f}s "
              f"(并发度: {best_latency_result.concurrency_level})")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
