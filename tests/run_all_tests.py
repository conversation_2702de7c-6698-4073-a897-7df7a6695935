#!/usr/bin/env python3
"""
统一测试运行脚本
运行所有测试套件并生成报告
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'openai-server'))

def run_test_file(test_file):
    """运行单个测试文件"""
    print(f"\n🧪 运行测试: {test_file}")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 运行测试
        result = subprocess.run(
            [sys.executable, str(project_root / 'tests' / test_file)],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=60
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {test_file} 通过 ({duration:.2f}秒)")
            return True, duration, result.stdout
        else:
            print(f"❌ {test_file} 失败 ({duration:.2f}秒)")
            print("错误输出:")
            print(result.stderr)
            return False, duration, result.stderr
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {test_file} 超时")
        return False, 60.0, "测试超时"
    except Exception as e:
        print(f"💥 {test_file} 异常: {e}")
        return False, 0.0, str(e)

def main():
    """主函数"""
    print("🚀 QJ Robots 感知API 测试套件")
    print("=" * 60)
    print(f"项目根目录: {project_root}")
    print(f"Python版本: {sys.version}")
    print("=" * 60)
    
    # 测试文件列表
    test_files = [
        'test_stage1_refactoring.py',
        'test_stage2_function_calling.py', 
        'test_integration_simple.py'
    ]
    
    # 运行测试统计
    total_tests = len(test_files)
    passed_tests = 0
    failed_tests = 0
    total_time = 0.0
    results = []
    
    # 运行所有测试
    for test_file in test_files:
        success, duration, output = run_test_file(test_file)
        total_time += duration
        
        if success:
            passed_tests += 1
        else:
            failed_tests += 1
            
        results.append({
            'file': test_file,
            'success': success,
            'duration': duration,
            'output': output
        })
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试报告")
    print("=" * 60)
    print(f"总测试文件: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    # 详细结果
    print("\n📋 详细结果:")
    for result in results:
        status = "✅ 通过" if result['success'] else "❌ 失败"
        print(f"  {result['file']:<35} {status} ({result['duration']:.2f}秒)")
    
    # 失败的测试详情
    if failed_tests > 0:
        print("\n❌ 失败的测试详情:")
        for result in results:
            if not result['success']:
                print(f"\n{result['file']}:")
                print(result['output'][:500] + "..." if len(result['output']) > 500 else result['output'])
    
    print("\n" + "=" * 60)
    if failed_tests == 0:
        print("🎉 所有测试通过！")
        return 0
    else:
        print(f"💥 {failed_tests} 个测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
