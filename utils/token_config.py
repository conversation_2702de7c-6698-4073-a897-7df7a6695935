#!/usr/bin/env python3
"""
Token configuration and management for QJ Robots OpenAI-compatible API.

This module manages the mapping between OpenAI-style access tokens and 
QJ Robots APP_ID/APP_SECRET pairs, allowing the server to authenticate
requests using standard OpenAI token format.
"""

import secrets
import string
from typing import Dict, Optional, <PERSON>ple
import json
import os
from dataclasses import dataclass, asdict


@dataclass
class TokenInfo:
    """Information associated with a token"""
    app_id: str
    app_secret: str
    description: str = ""
    created_at: str = ""
    last_used: str = ""
    usage_count: int = 0


class TokenManager:
    """Manages token-to-credentials mapping"""
    
    def __init__(self, config_file: str = "token_mapping.json"):
        self.config_file = config_file
        self.token_mapping: Dict[str, TokenInfo] = {}
        self.load_config()
    
    def generate_token(self, length: int = 51) -> str:
        """Generate a secure random token similar to OpenAI format
        
        OpenAI tokens typically start with 'sk-' and are ~51 characters total
        """
        # Generate random string for the token body
        alphabet = string.ascii_letters + string.digits
        token_body = ''.join(secrets.choice(alphabet) for _ in range(length - 3))
        return f"sk-{token_body}"
    
    def add_token(self, app_id: str, app_secret: str, description: str = "") -> str:
        """Add a new token mapping"""
        token = self.generate_token()
        
        # Ensure token is unique
        while token in self.token_mapping:
            token = self.generate_token()
        
        from datetime import datetime
        self.token_mapping[token] = TokenInfo(
            app_id=app_id,
            app_secret=app_secret,
            description=description,
            created_at=datetime.now().isoformat(),
            usage_count=0
        )
        
        return token
    
    def get_credentials(self, token: str) -> Optional[Tuple[str, str]]:
        """Get APP_ID and APP_SECRET for a given token"""
        if token in self.token_mapping:
            token_info = self.token_mapping[token]
            # Update usage tracking
            token_info.usage_count += 1
            from datetime import datetime
            token_info.last_used = datetime.now().isoformat()
            return token_info.app_id, token_info.app_secret
        return None
    
    def validate_token(self, token: str) -> bool:
        """Validate if a token exists and is valid"""
        return token in self.token_mapping
    
    def list_tokens(self) -> Dict[str, Dict]:
        """List all tokens with their info (excluding secrets)"""
        result = {}
        for token, info in self.token_mapping.items():
            result[token] = {
                "app_id": info.app_id,
                "description": info.description,
                "created_at": info.created_at,
                "last_used": info.last_used,
                "usage_count": info.usage_count,
                # Don't expose the secret
                "app_secret": "***hidden***"
            }
        return result
    
    def remove_token(self, token: str) -> bool:
        """Remove a token"""
        if token in self.token_mapping:
            del self.token_mapping[token]
            self.save_config()
            return True
        return False
    
    def save_config(self):
        """Save token mapping to file"""
        config_data = {}
        for token, info in self.token_mapping.items():
            config_data[token] = asdict(info)
        
        with open(self.config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def load_config(self):
        """Load token mapping from file"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                for token, info_dict in config_data.items():
                    self.token_mapping[token] = TokenInfo(**info_dict)
                    
            except Exception as e:
                print(f"Warning: Could not load token config: {e}")
                self.token_mapping = {}
        else:
            # Initialize with empty mapping
            self.token_mapping = {}


def generate_initial_tokens() -> Dict[str, str]:
    """Generate 10 initial tokens for configuration"""
    manager = TokenManager()
    tokens = {}
    
    print("Generating 10 OpenAI-compatible tokens...")
    print("=" * 60)
    
    for i in range(1, 11):
        # Generate token with placeholder credentials
        token = manager.add_token(
            app_id=f"YOUR_APP_ID_{i}",
            app_secret=f"YOUR_APP_SECRET_{i}",
            description=f"Token {i} - Replace with actual credentials"
        )
        tokens[token] = f"Token {i}"
        print(f"Token {i:2d}: {token}")
    
    # Save the configuration
    manager.save_config()
    
    print("=" * 60)
    print(f"Tokens saved to: {manager.config_file}")
    print("\nNext steps:")
    print("1. Edit token_mapping.json to replace placeholder credentials")
    print("2. Set your actual QJ_APP_ID and QJ_APP_SECRET values")
    print("3. Use these tokens in your OpenAI client as api_key")
    
    return tokens


def create_token_mapping_template():
    """Create a template configuration file"""
    template = {
        "sk-example1234567890abcdef1234567890abcdef123456789": {
            "app_id": "YOUR_ACTUAL_APP_ID_1",
            "app_secret": "YOUR_ACTUAL_APP_SECRET_1", 
            "description": "Production token for main application",
            "created_at": "2024-01-01T00:00:00",
            "last_used": "",
            "usage_count": 0
        },
        "sk-example2234567890abcdef1234567890abcdef123456789": {
            "app_id": "YOUR_ACTUAL_APP_ID_2",
            "app_secret": "YOUR_ACTUAL_APP_SECRET_2",
            "description": "Development token for testing",
            "created_at": "2024-01-01T00:00:00", 
            "last_used": "",
            "usage_count": 0
        }
    }
    
    with open("token_mapping_template.json", "w") as f:
        json.dump(template, f, indent=2)
    
    print("Template created: token_mapping_template.json")
    print("Copy this to token_mapping.json and fill in your actual credentials")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "template":
        create_token_mapping_template()
    else:
        # Generate initial tokens
        tokens = generate_initial_tokens()
        
        print("\n" + "=" * 60)
        print("IMPORTANT: Edit token_mapping.json and replace:")
        print("- YOUR_APP_ID_X with your actual QJ Robots APP_ID")
        print("- YOUR_APP_SECRET_X with your actual QJ Robots APP_SECRET")
        print("=" * 60)
