#!/usr/bin/env python3
"""
Token management CLI tool for QJ Robots OpenAI-compatible API.

This tool helps you manage API tokens and their associated credentials.
"""

import argparse
import json
import sys
from typing import Optional
from .token_config import TokenManager


def list_tokens(manager: TokenManager):
    """List all tokens"""
    tokens = manager.list_tokens()
    
    if not tokens:
        print("No tokens found.")
        return
    
    print(f"Found {len(tokens)} tokens:")
    print("-" * 80)
    
    for token, info in tokens.items():
        print(f"Token: {token}")
        print(f"  App ID: {info['app_id']}")
        print(f"  Description: {info['description']}")
        print(f"  Created: {info['created_at']}")
        print(f"  Last Used: {info['last_used'] or 'Never'}")
        print(f"  Usage Count: {info['usage_count']}")
        print("-" * 80)


def add_token(manager: TokenManager, app_id: str, app_secret: str, description: str = ""):
    """Add a new token"""
    try:
        token = manager.add_token(app_id, app_secret, description)
        manager.save_config()
        
        print("✅ Token created successfully!")
        print(f"Token: {token}")
        print(f"App ID: {app_id}")
        print(f"Description: {description}")
        print("\nYou can now use this token in your OpenAI client:")
        print(f'openai.api_key = "{token}"')
        
        return token
        
    except Exception as e:
        print(f"❌ Failed to create token: {e}")
        return None


def remove_token(manager: TokenManager, token: str):
    """Remove a token"""
    if manager.remove_token(token):
        print(f"✅ Token {token} removed successfully!")
    else:
        print(f"❌ Token {token} not found.")


def validate_token(manager: TokenManager, token: str):
    """Validate a token"""
    if manager.validate_token(token):
        creds = manager.get_credentials(token)
        print(f"✅ Token is valid")
        print(f"App ID: {creds[0] if creds else 'Unknown'}")
    else:
        print(f"❌ Token is invalid")


def update_credentials(manager: TokenManager, token: str, app_id: Optional[str] = None, 
                      app_secret: Optional[str] = None, description: Optional[str] = None):
    """Update token credentials"""
    if token not in manager.token_mapping:
        print(f"❌ Token {token} not found.")
        return
    
    token_info = manager.token_mapping[token]
    
    if app_id:
        token_info.app_id = app_id
        print(f"✅ Updated App ID to: {app_id}")
    
    if app_secret:
        token_info.app_secret = app_secret
        print(f"✅ Updated App Secret")
    
    if description is not None:
        token_info.description = description
        print(f"✅ Updated description to: {description}")
    
    manager.save_config()
    print("✅ Changes saved!")


def export_tokens(manager: TokenManager, filename: str):
    """Export tokens to a file"""
    try:
        tokens = manager.list_tokens()
        with open(filename, 'w') as f:
            json.dump(tokens, f, indent=2)
        print(f"✅ Tokens exported to {filename}")
    except Exception as e:
        print(f"❌ Failed to export tokens: {e}")


def show_usage_examples():
    """Show usage examples"""
    print("Usage Examples:")
    print("-" * 50)
    print("\n1. Using with OpenAI Python library:")
    print("```python")
    print("import openai")
    print("openai.api_base = 'http://localhost:8000/v1'")
    print("openai.api_key = 'sk-your-token-here'")
    print("")
    print("response = openai.ChatCompletion.create(")
    print("    model='qj-perception-v1',")
    print("    messages=[{'role': 'user', 'content': '...'}]")
    print(")")
    print("```")
    print("\n2. Using with curl:")
    print("```bash")
    print("curl -X POST http://localhost:8000/v1/chat/completions \\")
    print("  -H 'Authorization: Bearer sk-your-token-here' \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -d '{")
    print('    "model": "qj-perception-v1",')
    print('    "messages": [{"role": "user", "content": "..."}]')
    print("  }'")
    print("```")
    print("\n3. Using with requests:")
    print("```python")
    print("import requests")
    print("")
    print("headers = {")
    print("    'Authorization': 'Bearer sk-your-token-here',")
    print("    'Content-Type': 'application/json'")
    print("}")
    print("")
    print("response = requests.post(")
    print("    'http://localhost:8000/v1/chat/completions',")
    print("    headers=headers,")
    print("    json={'model': 'qj-perception-v1', 'messages': [...]}") 
    print(")")
    print("```")


def main():
    parser = argparse.ArgumentParser(
        description="Manage API tokens for QJ Robots OpenAI-compatible API",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List tokens
    subparsers.add_parser('list', help='List all tokens')
    
    # Add token
    add_parser = subparsers.add_parser('add', help='Add a new token')
    add_parser.add_argument('app_id', help='QJ Robots App ID')
    add_parser.add_argument('app_secret', help='QJ Robots App Secret')
    add_parser.add_argument('--description', '-d', default='', help='Token description')
    
    # Remove token
    remove_parser = subparsers.add_parser('remove', help='Remove a token')
    remove_parser.add_argument('token', help='Token to remove')
    
    # Validate token
    validate_parser = subparsers.add_parser('validate', help='Validate a token')
    validate_parser.add_argument('token', help='Token to validate')
    
    # Update token
    update_parser = subparsers.add_parser('update', help='Update token credentials')
    update_parser.add_argument('token', help='Token to update')
    update_parser.add_argument('--app-id', help='New App ID')
    update_parser.add_argument('--app-secret', help='New App Secret')
    update_parser.add_argument('--description', help='New description')
    
    # Export tokens
    export_parser = subparsers.add_parser('export', help='Export tokens to file')
    export_parser.add_argument('filename', help='Output filename')
    
    # Show examples
    subparsers.add_parser('examples', help='Show usage examples')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize token manager
    manager = TokenManager()
    
    # Execute command
    if args.command == 'list':
        list_tokens(manager)
    
    elif args.command == 'add':
        add_token(manager, args.app_id, args.app_secret, args.description)
    
    elif args.command == 'remove':
        remove_token(manager, args.token)
    
    elif args.command == 'validate':
        validate_token(manager, args.token)
    
    elif args.command == 'update':
        update_credentials(manager, args.token, args.app_id, args.app_secret, args.description)
    
    elif args.command == 'export':
        export_tokens(manager, args.filename)
    
    elif args.command == 'examples':
        show_usage_examples()


if __name__ == "__main__":
    main()
