#!/usr/bin/env python3
"""
下载测试图片脚本

下载一些包含车辆的测试图片，用于测试视觉 API。
"""

import requests
import os
from pathlib import Path

# 测试图片 URLs（包含车辆的图片）
TEST_IMAGES = {
    "cars_street.jpg": "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&q=80",
    "parking_lot.jpg": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&q=80",
    "traffic.jpg": "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=800&q=80",
    "red_car.jpg": "https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=800&q=80",
    "blue_car.jpg": "https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&q=80"
}

def download_image(url, filename):
    """下载图片"""
    try:
        print(f"📥 下载 {filename}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ {filename} 下载完成")
        return True
    except Exception as e:
        print(f"❌ {filename} 下载失败: {e}")
        return False

def main():
    """主函数"""
    print("🖼️  下载测试图片...")
    
    # 创建测试图片目录
    test_dir = Path("test_images")
    test_dir.mkdir(exist_ok=True)
    
    success_count = 0
    for filename, url in TEST_IMAGES.items():
        filepath = test_dir / filename
        if download_image(url, filepath):
            success_count += 1
    
    print(f"\n📊 下载完成: {success_count}/{len(TEST_IMAGES)} 张图片")
    
    if success_count > 0:
        print("\n🎯 现在您可以使用以下命令测试:")
        for filename in TEST_IMAGES.keys():
            filepath = test_dir / filename
            if filepath.exists():
                print(f"  python test_openai_vision_api.py {filepath}")
                break

if __name__ == "__main__":
    main()
