#!/usr/bin/env python3
"""
测试格式化输出功能

测试感知API结果的解析和格式化输出
"""

from openai import OpenAI
import json

# API 配置
BASE_URL = "http://localhost:8000"
API_KEY = "sk-WGCxElNXr1U4vloE5ATpycYTn7EZblkyxp4c090OVBsjxLwc"

def test_formatted_output():
    """测试格式化输出"""
    print("🎯 测试感知API结果格式化输出")
    print("=" * 60)
    
    # 初始化客户端
    client = OpenAI(api_key=API_KEY, base_url=BASE_URL)
    
    # 测试用例
    test_cases = [
        {
            "image_url": "https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=800&q=80",
            "question": "这张图片中有几辆车？",
            "description": "红色跑车图片"
        },
        {
            "image_url": "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&q=80",
            "question": "请分析图片中的车辆类型和颜色",
            "description": "街道车辆图片"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['description']}")
        print(f"🖼️  图片: {test_case['image_url']}")
        print(f"❓ 问题: {test_case['question']}")
        print("-" * 60)
        
        # 测试非流式API
        print("🔄 非流式API测试:")
        try:
            response = client.chat.completions.create(
                model="qj-perception-v1",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": test_case["question"]
                            },
                            {
                                "type": "image_url",
                                "image_url": {"url": test_case["image_url"]}
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            
            print("✅ 成功获得响应:")
            print(response.choices[0].message.content)
            
        except Exception as e:
            print(f"❌ 非流式API失败: {e}")
        
        print("\n" + "="*60)
        
        # 测试流式API
        print("🌊 流式API测试:")
        try:
            stream = client.chat.completions.create(
                model="qj-perception-v1",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": test_case["question"]
                            },
                            {
                                "type": "image_url",
                                "image_url": {"url": test_case["image_url"]}
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                stream=True
            )
            
            print("✅ 流式响应:")
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    print(chunk.choices[0].delta.content, end='', flush=True)
            
            print("\n✅ 流式响应完成")
            
        except Exception as e:
            print(f"❌ 流式API失败: {e}")
        
        print("\n" + "="*60)
        
        if i < len(test_cases):
            input("\n按回车键继续下一个测试用例...")

def main():
    """主函数"""
    print("🚀 感知API格式化输出测试工具")
    print("测试感知API结果的解析、格式化和流式输出功能")
    print()
    
    try:
        test_formatted_output()
        print("\n🎉 所有测试完成！")
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()
