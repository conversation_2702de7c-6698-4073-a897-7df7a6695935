#!/bin/bash

# 前后端分离部署脚本
# 用于部署感知API测试页面的静态文件

set -e

echo "🚀 开始部署感知API测试页面（前后端分离架构）..."

# 定义路径
STATIC_SOURCE_DIR="openai-server/static"
NGINX_STATIC_DIR="/opt/homebrew/var/www/perception"
NGINX_CONF_SOURCE="conf/nginx.conf"
NGINX_CONF_TARGET="/opt/homebrew/etc/nginx/nginx.conf"

# 1. 创建静态文件目录
echo "📁 创建静态文件目录..."
mkdir -p "$NGINX_STATIC_DIR"

# 2. 复制静态文件
echo "📋 复制静态文件到nginx目录..."
cp -r "$STATIC_SOURCE_DIR"/* "$NGINX_STATIC_DIR/"

# 3. 设置文件权限
echo "🔐 设置文件权限..."
chmod -R 644 "$NGINX_STATIC_DIR"/*
chmod 755 "$NGINX_STATIC_DIR"

# 4. 备份当前nginx配置
echo "💾 备份当前nginx配置..."
if [ -f "$NGINX_CONF_TARGET" ]; then
    sudo cp "$NGINX_CONF_TARGET" "$NGINX_CONF_TARGET.backup.$(date +%Y%m%d_%H%M%S)"
fi

# 5. 应用新的nginx配置
echo "⚙️ 应用新的nginx配置..."
sudo cp "$NGINX_CONF_SOURCE" "$NGINX_CONF_TARGET"

# 6. 测试nginx配置
echo "🧪 测试nginx配置语法..."
sudo nginx -t

# 7. 重新加载nginx
echo "🔄 重新加载nginx配置..."
sudo nginx -s reload

# 8. 显示部署信息
echo ""
echo "✅ 部署完成！"
echo ""
echo "📊 部署信息："
echo "   静态文件目录: $NGINX_STATIC_DIR"
echo "   访问地址: https://jwd.vooice.tech/perception/"
echo "   API地址: https://jwd.vooice.tech/openai/"
echo ""
echo "🌐 URL映射："
echo "   https://jwd.vooice.tech/perception/demo.html -> $NGINX_STATIC_DIR/demo.html"
echo "   https://jwd.vooice.tech/perception/demo.css -> $NGINX_STATIC_DIR/demo.css"
echo "   https://jwd.vooice.tech/perception/demo.js -> $NGINX_STATIC_DIR/demo.js"
echo "   https://jwd.vooice.tech/openai/v1/chat/completions -> http://localhost:8000/v1/chat/completions"
echo ""
echo "🧪 测试命令："
echo "   curl -I https://jwd.vooice.tech/perception/demo.html"
echo "   curl -I https://jwd.vooice.tech/perception/demo.css"
echo "   curl -I https://jwd.vooice.tech/perception/demo.js"
echo ""
echo "🎉 前后端分离架构部署成功！"
