#!/usr/bin/env python3
"""
测试所有模块导入是否正常工作
"""

def test_imports():
    """测试所有关键模块的导入"""
    
    print("Testing imports...")
    
    try:
        # 测试主模块
        from py_qj_robots.perception import Perception
        print("✓ py_qj_robots.perception import successful")
        
        from py_qj_robots.openai_api import OpenAIPerceptionAPI
        print("✓ py_qj_robots.openai_api import successful")
        
        # 测试 utils 模块
        from utils.token_config import TokenManager
        print("✓ utils.token_config import successful")
        
        # 测试 openai-server 模块（需要在正确的目录中）
        import sys
        import os
        
        # 添加 openai-server 目录到路径
        openai_server_path = os.path.join(os.path.dirname(__file__), 'openai-server')
        if openai_server_path not in sys.path:
            sys.path.insert(0, openai_server_path)
        
        from perception_handler import PerceptionHandler
        print("✓ perception_handler import successful")
        
        from response_generator import ResponseGenerator
        print("✓ response_generator import successful")
        
        from function_call_manager import FunctionCallManager
        print("✓ function_call_manager import successful")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_imports()
    exit(0 if success else 1)
