#!/bin/bash
# Start the OpenAI-compatible API server

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Starting QJ Robots OpenAI-compatible API server..."
echo "Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Start the server
echo "Starting server on http://localhost:8000"
python openai-server/openai_api_server.py
