#!/bin/bash
# Restart nginx with the updated configuration

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "🔧 重启 Nginx 服务..."
echo "项目根目录: $PROJECT_ROOT"

# 检查 nginx 配置文件语法
echo "📝 检查 nginx 配置文件语法..."
if nginx -t -c "$PROJECT_ROOT/conf/nginx.conf"; then
    echo "✅ nginx 配置文件语法正确"
else
    echo "❌ nginx 配置文件语法错误，请检查配置"
    exit 1
fi

# 重启 nginx
echo "🔄 重启 nginx 服务..."
if command -v brew >/dev/null 2>&1; then
    # macOS with Homebrew
    echo "使用 Homebrew 重启 nginx..."
    brew services restart nginx
elif command -v systemctl >/dev/null 2>&1; then
    # Linux with systemd
    echo "使用 systemctl 重启 nginx..."
    sudo systemctl restart nginx
elif command -v service >/dev/null 2>&1; then
    # Linux with service command
    echo "使用 service 命令重启 nginx..."
    sudo service nginx restart
else
    echo "⚠️  无法自动重启 nginx，请手动重启"
    echo "   配置文件位置: $PROJECT_ROOT/conf/nginx.conf"
    exit 1
fi

echo "✅ nginx 重启完成！"
echo ""
echo "📋 配置摘要:"
echo "   域名: jwd.vooice.tech"
echo "   路径: /v1/*"
echo "   转发到: http://localhost:8000/v1/"
echo "   SSL: 启用 (端口 443)"
echo ""
echo "🧪 测试命令:"
echo "   curl -k https://jwd.vooice.tech/v1/models"
echo "   curl -k https://jwd.vooice.tech/v1/health"
