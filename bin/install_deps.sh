#!/bin/bash
# Install dependencies for QJ Robots Python SDK

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Installing dependencies for QJ Robots Python SDK..."
echo "Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if virtual environment exists, if not create one
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

echo "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
if [ -f "conf/requirements-openai.txt" ]; then
    pip install -r conf/requirements-openai.txt
elif [ -f "conf/requirements.txt" ]; then
    pip install -r conf/requirements.txt
else
    echo "No requirements file found, installing basic dependencies..."
    pip install requests python-dotenv fastapi uvicorn pydantic
fi

echo "Dependencies installed successfully!"
echo "You can now run:"
echo "  ./bin/start_server.sh    # Start the API server"
echo "  ./bin/run_tests.sh       # Run tests"
