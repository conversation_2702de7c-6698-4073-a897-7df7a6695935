#!/bin/bash
# Run all tests for the QJ Robots Python SDK

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Running QJ Robots Python SDK tests..."
echo "Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if virtual environment exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

echo "Running OpenAI API tests..."
python tests/test_openai_api.py

echo ""
echo "Running token authentication tests..."
python tests/test_token_auth.py

echo ""
echo "Running installation verification..."
python tests/verify_installation.py

echo ""
echo "All tests completed!"
