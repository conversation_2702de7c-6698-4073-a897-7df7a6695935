#!/usr/bin/env python3
"""
Example usage of the OpenAI-compatible QJ Robots Perception API.

This example demonstrates how to use the perception capabilities through
an OpenAI-style interface, making it easy to integrate with existing
OpenAI-based applications.
"""

import json
import asyncio
from py_qj_robots.openai_api import OpenAIPerceptionAPI


def example_basic_usage():
    """Basic usage example - synchronous"""
    print("=== Basic Usage Example ===")
    
    # Initialize the API
    api = OpenAIPerceptionAPI()
    
    # Example messages with function call
    messages = [
        {
            "role": "system",
            "content": "You are a helpful assistant that can analyze images using perception functions."
        },
        {
            "role": "user",
            "content": '{"function": "check_image", "arguments": {"image_type": "2D", "image_url": "https://example.com/image.jpg", "object_names": ["apple", "banana", "orange"]}}'
        }
    ]
    
    try:
        # Create chat completion
        response = api.create_chat_completion(
            messages=messages,
            model="qj-perception-v1"
        )
        
        print("Response:")
        print(f"ID: {response.id}")
        print(f"Model: {response.model}")
        print(f"Content: {response.choices[0].message.content}")
        print(f"Usage: {response.usage.total_tokens} tokens")
        
    except Exception as e:
        print(f"Error: {e}")


def example_streaming_usage():
    """Streaming response example"""
    print("\n=== Streaming Usage Example ===")
    
    api = OpenAIPerceptionAPI()
    
    messages = [
        {
            "role": "user",
            "content": '{"function": "split_image", "arguments": {"image_type": "2D", "image_url": "https://example.com/image.jpg", "object_names": ["cup", "plate"]}}'
        }
    ]
    
    try:
        # Create streaming chat completion
        stream = api.create_chat_completion(
            messages=messages,
            stream=True
        )
        
        print("Streaming response:")
        for chunk in stream:
            if chunk.choices and chunk.choices[0].get("delta", {}).get("content"):
                print(chunk.choices[0]["delta"]["content"], end="", flush=True)
        print()  # New line after streaming
        
    except Exception as e:
        print(f"Error: {e}")


def example_function_listing():
    """List available functions"""
    print("\n=== Available Functions ===")
    
    api = OpenAIPerceptionAPI()
    functions = api.list_functions()
    
    for func in functions:
        func_info = func["function"]
        print(f"\nFunction: {func_info['name']}")
        print(f"Description: {func_info['description']}")
        print("Required parameters:", func_info["parameters"]["required"])


def example_different_functions():
    """Examples of different perception functions"""
    print("\n=== Different Function Examples ===")
    
    api = OpenAIPerceptionAPI()
    
    # Example function calls for different perception tasks
    examples = [
        {
            "name": "Object Detection",
            "message": '{"function": "check_image", "arguments": {"image_type": "2D", "image_url": "https://example.com/scene.jpg", "object_names": ["person", "car", "bicycle"]}}'
        },
        {
            "name": "Image Segmentation", 
            "message": '{"function": "split_image", "arguments": {"image_type": "2D", "image_url": "https://example.com/objects.jpg", "object_names": ["apple", "orange"]}}'
        },
        {
            "name": "Property Description",
            "message": '{"function": "props_describe", "arguments": {"image_type": "2D", "image_url": "https://example.com/fruit.jpg", "object_names": ["apple"], "questions": ["What color is it?", "Is it ripe?"]}}'
        },
        {
            "name": "Angle Prediction",
            "message": '{"function": "angle_prediction", "arguments": {"image_type": "2D", "image_url": "https://example.com/tools.jpg", "object_names": ["screwdriver", "wrench"]}}'
        },
        {
            "name": "Key Point Detection",
            "message": '{"function": "key_point_prediction", "arguments": {"image_type": "2D", "image_url": "https://example.com/parts.jpg", "object_names": ["connector", "cable"]}}'
        },
        {
            "name": "Grasp Point Prediction",
            "message": '{"function": "grab_point_prediction", "arguments": {"image_type": "2D", "image_url": "https://example.com/items.jpg", "object_names": ["bottle", "cup"]}}'
        },
        {
            "name": "Full Perception Analysis",
            "message": '{"function": "full_perception", "arguments": {"image_type": "2D", "image_url": "https://example.com/complex.jpg", "object_names": ["robot", "tool"], "questions": ["What is the condition?", "Is it operational?"]}}'
        }
    ]
    
    for example in examples:
        print(f"\n--- {example['name']} ---")
        print(f"Function call: {example['message']}")
        
        # Note: These would fail with actual API calls since the URLs are fake
        # In real usage, you would provide valid image URLs


def example_async_usage():
    """Asynchronous usage example"""
    print("\n=== Async Usage Example ===")
    
    # Initialize with async mode enabled
    api = OpenAIPerceptionAPI(enable_async_mode=True, max_workers=5)
    
    def completion_callback(result, error):
        if error:
            print(f"Async completion failed: {error}")
        else:
            print(f"Async completion succeeded:")
            print(f"Content: {result.choices[0].message.content}")
    
    messages = [
        {
            "role": "user",
            "content": '{"function": "check_image", "arguments": {"image_type": "2D", "image_url": "https://example.com/async_image.jpg", "object_names": ["cat", "dog"]}}'
        }
    ]
    
    try:
        # Submit async completion
        task_id = api.create_chat_completion_async(
            messages=messages,
            callback=completion_callback,
            timeout=60
        )
        
        print(f"Async task submitted with ID: {task_id}")
        print("Callback will be called when complete...")
        
        # In a real application, you would continue with other work
        # while the async task processes in the background
        
    except Exception as e:
        print(f"Error: {e}")


def example_error_handling():
    """Error handling examples"""
    print("\n=== Error Handling Examples ===")
    
    api = OpenAIPerceptionAPI()
    
    # Example 1: Invalid function name
    try:
        messages = [{"role": "user", "content": '{"function": "invalid_function", "arguments": {}}'}]
        response = api.create_chat_completion(messages=messages)
    except Exception as e:
        print(f"Invalid function error: {e}")
    
    # Example 2: Missing required parameters
    try:
        messages = [{"role": "user", "content": '{"function": "check_image", "arguments": {"image_type": "2D"}}'}]
        response = api.create_chat_completion(messages=messages)
    except Exception as e:
        print(f"Missing parameters error: {e}")
    
    # Example 3: No function call in message
    try:
        messages = [{"role": "user", "content": "Hello, how are you?"}]
        response = api.create_chat_completion(messages=messages)
        print("No function call - helpful response:")
        print(response.choices[0].message.content[:200] + "...")
    except Exception as e:
        print(f"No function call error: {e}")


if __name__ == "__main__":
    print("QJ Robots OpenAI-Compatible Perception API Examples")
    print("=" * 50)
    
    # Run examples
    example_function_listing()
    example_different_functions()
    example_basic_usage()
    example_streaming_usage()
    example_async_usage()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nNote: Most examples will show errors since they use fake image URLs.")
    print("Replace with real image URLs to test actual functionality.")
