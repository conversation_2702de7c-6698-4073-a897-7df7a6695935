"""
异步模式使用示例

这个示例展示了如何使用异步模式来实现高性能的感知API调用。
异步模式将任务提交和结果获取分离，可以实现更高的QPS。
"""

import os
import time
import threading
from dotenv import load_dotenv
from py_qj_robots import Perception


def init_async_perception():
    """初始化异步模式的Perception实例"""
    load_dotenv()
    
    # 启用异步模式，设置工作线程数
    return Perception(enable_async_mode=True, max_workers=20)


def example_1_basic_async():
    """示例1: 基本异步调用"""
    print("=== 示例1: 基本异步调用 ===")
    
    perception = init_async_perception()
    image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
    
    # 定义回调函数
    def result_callback(result, error):
        if error:
            print(f"任务失败: {error}")
        else:
            print(f"任务成功完成: {result.get('taskStatus', 'Unknown')}")
    
    # 异步提交任务
    task_id = perception.check_image_async(
        image_type="2D",
        image_url=image_url,
        object_names=["bottle", "cup"],
        callback=result_callback,
        timeout=30
    )
    
    print(f"任务已提交，Task ID: {task_id}")
    
    # 可以继续做其他事情，结果会通过回调函数返回
    time.sleep(5)  # 等待一段时间让任务完成


def example_2_high_concurrency():
    """示例2: 高并发场景"""
    print("\n=== 示例2: 高并发场景 ===")
    
    perception = init_async_perception()
    image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
    
    # 统计完成情况
    completed_count = 0
    failed_count = 0
    lock = threading.Lock()
    
    def result_callback(result, error):
        nonlocal completed_count, failed_count
        with lock:
            if error:
                failed_count += 1
                print(f"任务失败 #{failed_count}: {error}")
            else:
                completed_count += 1
                print(f"任务完成 #{completed_count}")
    
    # 快速提交多个任务
    task_count = 20
    task_ids = []
    
    start_time = time.time()
    
    for i in range(task_count):
        task_id = perception.check_image_async(
            image_type="2D",
            image_url=image_url,
            object_names=["bottle", "cup"],
            callback=result_callback,
            timeout=30
        )
        task_ids.append(task_id)
    
    submit_time = time.time() - start_time
    print(f"提交 {task_count} 个任务耗时: {submit_time:.2f} 秒")
    print(f"提交QPS: {task_count / submit_time:.2f}")
    
    # 等待所有任务完成
    print("等待所有任务完成...")
    while completed_count + failed_count < task_count:
        time.sleep(0.1)
    
    total_time = time.time() - start_time
    print(f"所有任务完成，总耗时: {total_time:.2f} 秒")
    print(f"成功: {completed_count}, 失败: {failed_count}")
    print(f"整体QPS: {completed_count / total_time:.2f}")


def example_3_mixed_mode():
    """示例3: 混合使用同步和异步模式"""
    print("\n=== 示例3: 混合使用同步和异步模式 ===")
    
    # 同步模式 - 适合需要立即获得结果的场景
    sync_perception = Perception()  # 默认同步模式
    
    # 异步模式 - 适合高并发场景
    async_perception = init_async_perception()
    
    image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
    
    # 同步调用 - 立即获得结果
    print("执行同步调用...")
    sync_start = time.time()
    try:
        sync_result = sync_perception.check_image(
            image_type="2D",
            image_url=image_url,
            object_names=["bottle", "cup"]
        )
        sync_time = time.time() - sync_start
        print(f"同步调用完成，耗时: {sync_time:.2f} 秒")
    except Exception as e:
        print(f"同步调用失败: {e}")
    
    # 异步调用 - 快速提交
    print("执行异步调用...")
    async_start = time.time()
    
    def async_callback(result, error):
        async_time = time.time() - async_start
        if error:
            print(f"异步调用失败: {error}")
        else:
            print(f"异步调用完成，总耗时: {async_time:.2f} 秒")
    
    task_id = async_perception.check_image_async(
        image_type="2D",
        image_url=image_url,
        object_names=["bottle", "cup"],
        callback=async_callback,
        timeout=30
    )
    
    submit_time = time.time() - async_start
    print(f"异步任务提交完成，提交耗时: {submit_time:.2f} 秒，Task ID: {task_id}")
    
    # 等待异步任务完成
    time.sleep(5)


def example_4_manual_polling():
    """示例4: 手动轮询结果"""
    print("\n=== 示例4: 手动轮询结果 ===")
    
    perception = init_async_perception()
    image_url = "https://cos-cdn-v1.qj-robots.com/temp/3Fvk3RNcTGeJP5GWEmRqjh8Zb23NbtL2"
    
    # 提交任务但不设置回调
    task_id = perception.check_image_async(
        image_type="2D",
        image_url=image_url,
        object_names=["bottle", "cup"],
        callback=None,  # 不设置回调
        timeout=30
    )
    
    print(f"任务已提交，Task ID: {task_id}")
    
    # 手动轮询结果
    print("手动轮询结果...")
    while True:
        try:
            result = perception.get_task_result_sync(task_id)
            if result['taskStatus'] == 'DONE':
                print(f"任务完成: {result}")
                break
            elif result['taskStatus'] == 'SUBMIT_FAILED':
                print(f"任务提交失败")
                break
            else:
                print(f"任务状态: {result['taskStatus']}")
                time.sleep(1)
        except Exception as e:
            print(f"轮询出错: {e}")
            break


def main():
    """主函数"""
    try:
        # 运行各个示例
        example_1_basic_async()
        example_2_high_concurrency()
        example_3_mixed_mode()
        example_4_manual_polling()
        
        print("\n=== 所有示例执行完成 ===")
        
    except Exception as e:
        print(f"示例执行失败: {e}")


if __name__ == '__main__':
    main()
